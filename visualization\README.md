# 穿越沙漠数学建模项目 - 可视化分析报告

## 项目概述

本项目是一个严格按照原题规则实现的穿越沙漠数学建模问题求解器，通过优化路径规划和策略选择来最大化收益。项目包含6个关卡的完整求解方案，并提供了丰富的数据可视化分析。

## 关卡汇总分析

基于现有数据的分析结果：

| 关卡 | 策略类型 | 最终资金 | 净利润 | 用时 | 状态 |
|------|----------|----------|--------|------|------|
| 1 | direct | 9,410 | -590 | 3 | ❌亏损 |
| 2 | mining | 20,085 | 10,085 | 22 | ✅盈利 |
| 3 | mining | 8,915 | -1,085 | 7 | ❌亏损 |
| 4 | mining | 22,550 | 12,550 | 23 | ✅盈利 |
| 5 | direct | 9,510 | -490 | 3 | ❌亏损 |
| 6 | mining | 22,550 | 12,550 | 23 | ✅盈利 |

**总净利润**: 33,020 元  
**平均净利润**: 5,503 元  
**盈利成功率**: 50.0%

## 关键发现

1. **最佳盈利关卡**: 关卡4和关卡6并列，净利润均为12,550元
2. **主要策略**: mining策略使用4次，direct策略使用2次
3. **策略效果**: mining策略在长时间关卡中表现更好
4. **时间因素**: 用时较长的关卡(20+天)更容易实现盈利
5. **多人竞争**: 关卡5作为双人博弈关卡，时间压力大导致亏损

## 生成的可视化内容

### 1. 统计图表

#### 已生成的图表：
- **关卡对比分析** (`charts/level_comparison.png`): 各关卡净利润、最终资金、用时和策略分布对比
- **趋势分析** (`charts/trend_analysis.png`): 各项指标的变化趋势和效率分析

#### 可生成的图表：
- **资源消耗分析**: 水、食物消耗和天气影响分析
- **策略分析**: 不同策略效果和关卡难度分析
- **数学模型汇总**: 各关卡数学模型参数汇总

### 2. 流程图

使用Mermaid语法创建的交互式流程图：

#### 🔄 算法整体流程图
展示完整的求解过程，从配置读取到结果输出的全流程。

#### 🎯 单关卡求解流程图  
详细的单个关卡处理逻辑，包括路径规划和策略枚举。

#### 🌳 决策树流程图
实时决策的逻辑结构，展示不同条件下的策略选择。

#### 📊 数据流图
系统各组件间的数据流动，从输入到可视化输出的完整数据链。

## 技术实现

### 数据处理
- **数据来源**: 项目results目录下的JSON和CSV文件
- **数据格式**: 
  - `level_*_summary.json`: 关卡汇总结果
  - `level_*_daily_log.csv`: 每日详细记录
  - `level_*_mathematical_model.json`: 数学模型定义

### 可视化工具
- **Python库**: matplotlib, seaborn, pandas, numpy
- **图表格式**: PNG格式，300 DPI高清输出
- **流程图**: Mermaid语法，支持交互式渲染
- **字体支持**: 完整的中文显示支持

### 文件结构
```
visualization/
├── generate_charts.py          # 完整版图表生成器
├── simple_charts.py           # 简化版图表生成器
├── generate_flowcharts.md     # Mermaid流程图源码
├── requirements.txt           # Python依赖
└── README.md                  # 本文档

charts/                        # 输出目录
├── level_comparison.png       # 关卡对比图
├── trend_analysis.png         # 趋势分析图
└── [其他图表...]
```

## 使用方法

### 生成统计图表
```bash
# 安装依赖
pip install -r visualization/requirements.txt

# 生成完整图表集
python visualization/generate_charts.py

# 或生成基础图表
python visualization/simple_charts.py
```

### 查看流程图
1. **在线查看**: 将`generate_flowcharts.md`中的Mermaid代码复制到 https://mermaid.live/
2. **VS Code**: 安装Mermaid Preview插件
3. **GitHub**: 直接在仓库中查看markdown文件

## 数学模型特点

### 模型类型
- **混合整数规划 (MIP)**: 适用于所有关卡
- **动态规划**: 天气已知的关卡使用
- **随机策略**: 天气未知的关卡使用

### 核心约束
1. **时间约束**: 总用时 ≤ 时间限制
2. **负重约束**: 每日负重 ≤ 负重上限  
3. **资源约束**: 水和食物 ≥ 0
4. **沙暴约束**: 沙暴日必须停留
5. **路径约束**: 路径连续且到达终点

### 目标函数
最大化最终收益：`max Z = M_T + 0.5 × P_w × w_T + 0.5 × P_f × f_T`

## 结论与建议

### 策略建议
1. **优先选择mining策略**: 在时间充足的关卡中
2. **关注天气信息**: 天气已知的关卡表现更好
3. **合理规划时间**: 避免过于紧迫的时间安排
4. **多人关卡谨慎**: 竞争环境下需要更保守的策略

### 模型优化方向
1. **引入机器学习**: 预测最优挖矿天数
2. **多目标优化**: 平衡收益和风险
3. **实时调整**: 根据实际情况动态调整策略
4. **并行计算**: 提高大规模问题的求解效率

---

*报告生成时间: 2025-01-02*  
*项目地址: 穿越沙漠数学建模求解器*
