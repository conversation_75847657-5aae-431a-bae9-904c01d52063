========================================================================================================================
                                                   穿越沙漠游戏 - 完整数学建模报告                                                    
========================================================================================================================
生成时间: 2025-08-01 21:06:20
内容: 所有6个关卡的完整数学模型定义
========================================================================================================================

📋 数学建模说明:
   本报告包含每个关卡的完整数学模型，包括：
   • 决策变量定义
   • 参数设置
   • 约束条件
   • 目标函数
   • 求解方法
   • 复杂度分析


====================================================================================================
                                             第1关完整数学模型                                              
====================================================================================================

📋 问题描述:
   关卡特点: 单人基础关卡，天气完全已知，直接路径优化
   玩家数量: 1人
   地图规模: 27个节点
   时间限制: 30天
   天气信息: 完全已知
   矿山数量: 1个
   村庄数量: 1个

🔢 决策变量定义:
   空间变量:
     x_ij^t ∈ {0,1}  : 第t天从节点i移动到节点j (i,j ∈ {1,2,...,27})
     s_i^t ∈ {0,1}   : 第t天在节点i停留
     l_i^t ∈ {0,1}   : 第t天玩家位于节点i

   资源变量:
     w_t ∈ ℕ         : 第t天结束时的水量 (箱)
     f_t ∈ ℕ         : 第t天结束时的食物量 (箱)
     w_0, f_0 ∈ ℕ    : 初始购买的水量和食物量

   行动变量:
     m_i^t ∈ {0,1}   : 第t天在矿山i挖矿
     b_i^t ∈ ℕ       : 第t天在村庄i购买的资源量

   状态变量:
     M_t ∈ ℝ+        : 第t天结束时的资金
     arrived_t ∈ {0,1} : 第t天是否到达终点

📊 参数定义:
   地图参数:
     N = 27           : 节点总数
     T = 30            : 时间限制 (天)
     S = 1             : 起点节点
     E = 27             : 终点节点
     M = [12]        : 矿山节点集合
     V = [15]      : 村庄节点集合

   经济参数:
     M_0 = 10000        : 初始资金 (元)
     P_w = 5             : 水价格 (元/箱)
     P_f = 10            : 食物价格 (元/箱)
     R = 1000          : 挖矿基础收益 (元/天)

   物理参数:
     W_max = 1200       : 负重上限 (kg)
     w_weight = 3         : 水重量 (kg/箱)
     f_weight = 2         : 食物重量 (kg/箱)

   消耗参数:
     晴朗天气: 水5箱/天, 食物7箱/天
     高温天气: 水8箱/天, 食物6箱/天
     沙暴天气: 水10箱/天, 食物10箱/天
     α = 2.0           : 移动消耗倍数
     β = 1.5           : 挖矿消耗倍数

   天气参数 (已知):
     weather_t ∈ {'晴朗','高温','沙暴'} : 第t天天气状况
     天气预报: 30天完整预报

⚖️ 约束条件:
   1. 初始条件约束:
      l_1^0 = 1                    (初始位置在起点)
      l_i^0 = 0, ∀i ≠ 1           (其他位置为0)
      M_0 = 10000 - P_w·w_0 - P_f·f_0    (初始资金扣除购买成本)

   2. 位置唯一性约束:
      Σ_i l_i^t = 1, ∀t ∈ {0,1,...,T}      (每天只能在一个位置)

   3. 移动合法性约束:
      x_ij^t = 1 ⟹ (i,j) ∈ E_adj           (只能在相邻节点间移动)
      Σ_j x_ij^t + s_i^t = l_i^{t-1}, ∀i,t  (位置连续性)
      Σ_i x_ij^t = l_j^t, ∀j,t              (到达位置确定性)

   4. 沙暴日约束:
      weather_t = '沙暴' ⟹ x_ij^t = 0, ∀i,j  (沙暴日必须停留)

   5. 资源平衡约束:
      w_t = w_{t-1} - C_w^t + B_w^t, ∀t    (水量平衡)
      f_t = f_{t-1} - C_f^t + B_f^t, ∀t    (食物平衡)
      其中:
        C_w^t = Σ_i c_w(weather_t)·(α·Σ_j x_ij^t + β·m_i^t + s_i^t)·l_i^{t-1}
        C_f^t = Σ_i c_f(weather_t)·(α·Σ_j x_ij^t + β·m_i^t + s_i^t)·l_i^{t-1}
        B_w^t = Σ_{i∈V} b_w^{i,t}         (村庄购买的水)
        B_f^t = Σ_{i∈V} b_f^{i,t}         (村庄购买的食物)

   6. 负重约束:
      w_t·w_weight + f_t·f_weight ≤ W_max, ∀t  (每天负重不超限)

   7. 资源非负约束:
      w_t ≥ 0, f_t ≥ 0, ∀t                  (资源量非负)

   8. 挖矿约束:
      m_i^t = 1 ⟹ i ∈ M ∧ l_i^t = 1        (只能在矿山挖矿)
      到达矿山当天不能挖矿

   9. 村庄购买约束:
      b_i^t > 0 ⟹ i ∈ V ∧ l_i^t = 1        (只能在村庄购买)
      M_t ≥ 2·P_w·b_w^{i,t} + 2·P_f·b_f^{i,t}  (资金充足)

   10. 终点约束:
       arrived_t = 1 ⟹ l_27^t = 1     (到达终点)
       arrived_t = 1 ⟹ arrived_{t'} = 1, ∀t' ≥ t  (到达后保持)

   11. 时间约束:
       arrived_T = 1                         (必须在截止日期前到达)

🎯 目标函数:
   最大化最终资产价值:

   max Z = M_T + 0.5·P_w·w_T + 0.5·P_f·f_T

   其中:
     M_T = M_0 - P_w·w_0 - P_f·f_0 + Σ_{t=1}^T Σ_{i∈M} R·m_i^t - Σ_{t=1}^T Σ_{i∈V} (2·P_w·b_w^{i,t} + 2·P_f·b_f^{i,t})

   目标函数组成部分:
     • M_T: 最终现金
     • 0.5·P_w·w_T: 剩余水的退回价值 (基准价格的50%)
     • 0.5·P_f·f_T: 剩余食物的退回价值 (基准价格的50%)

   收入来源:
     • 挖矿收入: Σ_{t=1}^T Σ_{i∈M} R·m_i^t

   支出项目:
     • 初始购买: P_w·w_0 + P_f·f_0
     • 村庄购买: Σ_{t=1}^T Σ_{i∈V} (2·P_w·b_w^{i,t} + 2·P_f·b_f^{i,t})

🔧 求解方法:
   主要方法: 动态规划 (Dynamic Programming)
   时间复杂度: O(N²·T·W_max)
   方法描述: 天气完全已知，可以使用确定性动态规划

   具体步骤:
     1. 状态定义: dp[t][i][w][f] = 在第t天位于节点i，拥有w箱水、f箱食物时的最大资产
     2. 状态转移: 考虑所有可能的行动(移动/停留/挖矿/购买)
     3. 边界条件: dp[0][S][w_0][f_0] = M_0
     4. 目标状态: max{dp[t][E][w][f] | t ≤ T, w ≥ 0, f ≥ 0}

   模型类型: 确定性MINLP
   变量类型: 混合整数变量 (连续变量 + 0-1变量)

📈 复杂度分析:
   问题规模:
     • 节点数: N = 27
     • 时间跨度: T = 30
     • 最大负重: W = 1200
     • 玩家数: P = 1

   变量数量估算:
     • 位置变量: N·T = 810
     • 移动变量: N²·T = 21870
     • 资源变量: 2·T = 60
     • 行动变量: 约 60
     • 总变量数: 约 21960

   约束数量估算:
     • 位置约束: N·T = 810
     • 资源约束: 4·T = 120
     • 逻辑约束: 约 60
     • 总约束数: 约 990

   计算复杂度:
     • 状态空间: O(T·N·W²) ≈ O(30·27·120²)
     • 时间复杂度: O(T·N³·W²)
     • 空间复杂度: O(T·N·W²)
     • 问题类别: NP-Hard (但可用DP在伪多项式时间内求解)

   求解难度评估: 中等
   主要挑战: 状态空间爆炸


====================================================================================================
                                             第2关完整数学模型                                              
====================================================================================================

📋 问题描述:
   关卡特点: 单人复杂关卡，多矿山多村庄，长期挖矿策略
   玩家数量: 1人
   地图规模: 64个节点
   时间限制: 30天
   天气信息: 完全已知
   矿山数量: 2个
   村庄数量: 2个

🔢 决策变量定义:
   空间变量:
     x_ij^t ∈ {0,1}  : 第t天从节点i移动到节点j (i,j ∈ {1,2,...,64})
     s_i^t ∈ {0,1}   : 第t天在节点i停留
     l_i^t ∈ {0,1}   : 第t天玩家位于节点i

   资源变量:
     w_t ∈ ℕ         : 第t天结束时的水量 (箱)
     f_t ∈ ℕ         : 第t天结束时的食物量 (箱)
     w_0, f_0 ∈ ℕ    : 初始购买的水量和食物量

   行动变量:
     m_i^t ∈ {0,1}   : 第t天在矿山i挖矿
     b_i^t ∈ ℕ       : 第t天在村庄i购买的资源量

   状态变量:
     M_t ∈ ℝ+        : 第t天结束时的资金
     arrived_t ∈ {0,1} : 第t天是否到达终点

📊 参数定义:
   地图参数:
     N = 64           : 节点总数
     T = 30            : 时间限制 (天)
     S = 1             : 起点节点
     E = 64             : 终点节点
     M = [30, 55]        : 矿山节点集合
     V = [39, 62]      : 村庄节点集合

   经济参数:
     M_0 = 10000        : 初始资金 (元)
     P_w = 5             : 水价格 (元/箱)
     P_f = 10            : 食物价格 (元/箱)
     R = 1000          : 挖矿基础收益 (元/天)

   物理参数:
     W_max = 1200       : 负重上限 (kg)
     w_weight = 3         : 水重量 (kg/箱)
     f_weight = 2         : 食物重量 (kg/箱)

   消耗参数:
     晴朗天气: 水5箱/天, 食物7箱/天
     高温天气: 水8箱/天, 食物6箱/天
     沙暴天气: 水10箱/天, 食物10箱/天
     α = 2.0           : 移动消耗倍数
     β = 1.5           : 挖矿消耗倍数

   天气参数 (已知):
     weather_t ∈ {'晴朗','高温','沙暴'} : 第t天天气状况
     天气预报: 30天完整预报

⚖️ 约束条件:
   1. 初始条件约束:
      l_1^0 = 1                    (初始位置在起点)
      l_i^0 = 0, ∀i ≠ 1           (其他位置为0)
      M_0 = 10000 - P_w·w_0 - P_f·f_0    (初始资金扣除购买成本)

   2. 位置唯一性约束:
      Σ_i l_i^t = 1, ∀t ∈ {0,1,...,T}      (每天只能在一个位置)

   3. 移动合法性约束:
      x_ij^t = 1 ⟹ (i,j) ∈ E_adj           (只能在相邻节点间移动)
      Σ_j x_ij^t + s_i^t = l_i^{t-1}, ∀i,t  (位置连续性)
      Σ_i x_ij^t = l_j^t, ∀j,t              (到达位置确定性)

   4. 沙暴日约束:
      weather_t = '沙暴' ⟹ x_ij^t = 0, ∀i,j  (沙暴日必须停留)

   5. 资源平衡约束:
      w_t = w_{t-1} - C_w^t + B_w^t, ∀t    (水量平衡)
      f_t = f_{t-1} - C_f^t + B_f^t, ∀t    (食物平衡)
      其中:
        C_w^t = Σ_i c_w(weather_t)·(α·Σ_j x_ij^t + β·m_i^t + s_i^t)·l_i^{t-1}
        C_f^t = Σ_i c_f(weather_t)·(α·Σ_j x_ij^t + β·m_i^t + s_i^t)·l_i^{t-1}
        B_w^t = Σ_{i∈V} b_w^{i,t}         (村庄购买的水)
        B_f^t = Σ_{i∈V} b_f^{i,t}         (村庄购买的食物)

   6. 负重约束:
      w_t·w_weight + f_t·f_weight ≤ W_max, ∀t  (每天负重不超限)

   7. 资源非负约束:
      w_t ≥ 0, f_t ≥ 0, ∀t                  (资源量非负)

   8. 挖矿约束:
      m_i^t = 1 ⟹ i ∈ M ∧ l_i^t = 1        (只能在矿山挖矿)
      到达矿山当天不能挖矿

   9. 村庄购买约束:
      b_i^t > 0 ⟹ i ∈ V ∧ l_i^t = 1        (只能在村庄购买)
      M_t ≥ 2·P_w·b_w^{i,t} + 2·P_f·b_f^{i,t}  (资金充足)

   10. 终点约束:
       arrived_t = 1 ⟹ l_64^t = 1     (到达终点)
       arrived_t = 1 ⟹ arrived_{t'} = 1, ∀t' ≥ t  (到达后保持)

   11. 时间约束:
       arrived_T = 1                         (必须在截止日期前到达)

🎯 目标函数:
   最大化最终资产价值:

   max Z = M_T + 0.5·P_w·w_T + 0.5·P_f·f_T

   其中:
     M_T = M_0 - P_w·w_0 - P_f·f_0 + Σ_{t=1}^T Σ_{i∈M} R·m_i^t - Σ_{t=1}^T Σ_{i∈V} (2·P_w·b_w^{i,t} + 2·P_f·b_f^{i,t})

   目标函数组成部分:
     • M_T: 最终现金
     • 0.5·P_w·w_T: 剩余水的退回价值 (基准价格的50%)
     • 0.5·P_f·f_T: 剩余食物的退回价值 (基准价格的50%)

   收入来源:
     • 挖矿收入: Σ_{t=1}^T Σ_{i∈M} R·m_i^t

   支出项目:
     • 初始购买: P_w·w_0 + P_f·f_0
     • 村庄购买: Σ_{t=1}^T Σ_{i∈V} (2·P_w·b_w^{i,t} + 2·P_f·b_f^{i,t})

🔧 求解方法:
   主要方法: 动态规划 (Dynamic Programming)
   时间复杂度: O(N²·T·W_max)
   方法描述: 天气完全已知，可以使用确定性动态规划

   具体步骤:
     1. 状态定义: dp[t][i][w][f] = 在第t天位于节点i，拥有w箱水、f箱食物时的最大资产
     2. 状态转移: 考虑所有可能的行动(移动/停留/挖矿/购买)
     3. 边界条件: dp[0][S][w_0][f_0] = M_0
     4. 目标状态: max{dp[t][E][w][f] | t ≤ T, w ≥ 0, f ≥ 0}

   模型类型: 确定性MINLP
   变量类型: 混合整数变量 (连续变量 + 0-1变量)

📈 复杂度分析:
   问题规模:
     • 节点数: N = 64
     • 时间跨度: T = 30
     • 最大负重: W = 1200
     • 玩家数: P = 1

   变量数量估算:
     • 位置变量: N·T = 1920
     • 移动变量: N²·T = 122880
     • 资源变量: 2·T = 60
     • 行动变量: 约 120
     • 总变量数: 约 122970

   约束数量估算:
     • 位置约束: N·T = 1920
     • 资源约束: 4·T = 120
     • 逻辑约束: 约 60
     • 总约束数: 约 2100

   计算复杂度:
     • 状态空间: O(T·N·W²) ≈ O(30·64·120²)
     • 时间复杂度: O(T·N³·W²)
     • 空间复杂度: O(T·N·W²)
     • 问题类别: NP-Hard (但可用DP在伪多项式时间内求解)

   求解难度评估: 中等
   主要挑战: 状态空间爆炸


====================================================================================================
                                             第3关完整数学模型                                              
====================================================================================================

📋 问题描述:
   关卡特点: 单人挑战关卡，天气未知，时间限制严格
   玩家数量: 1人
   地图规模: 13个节点
   时间限制: 10天
   天气信息: 仅知当天
   矿山数量: 1个
   村庄数量: 0个

🔢 决策变量定义:
   空间变量:
     x_ij^t ∈ {0,1}  : 第t天从节点i移动到节点j (i,j ∈ {1,2,...,13})
     s_i^t ∈ {0,1}   : 第t天在节点i停留
     l_i^t ∈ {0,1}   : 第t天玩家位于节点i

   资源变量:
     w_t ∈ ℕ         : 第t天结束时的水量 (箱)
     f_t ∈ ℕ         : 第t天结束时的食物量 (箱)
     w_0, f_0 ∈ ℕ    : 初始购买的水量和食物量

   行动变量:
     m_i^t ∈ {0,1}   : 第t天在矿山i挖矿
     b_i^t ∈ ℕ       : 第t天在村庄i购买的资源量

   状态变量:
     M_t ∈ ℝ+        : 第t天结束时的资金
     arrived_t ∈ {0,1} : 第t天是否到达终点

📊 参数定义:
   地图参数:
     N = 13           : 节点总数
     T = 10            : 时间限制 (天)
     S = 1             : 起点节点
     E = 13             : 终点节点
     M = [9]        : 矿山节点集合
     V = []      : 村庄节点集合

   经济参数:
     M_0 = 10000        : 初始资金 (元)
     P_w = 5             : 水价格 (元/箱)
     P_f = 10            : 食物价格 (元/箱)
     R = 200          : 挖矿基础收益 (元/天)

   物理参数:
     W_max = 1200       : 负重上限 (kg)
     w_weight = 3         : 水重量 (kg/箱)
     f_weight = 2         : 食物重量 (kg/箱)

   消耗参数:
     晴朗天气: 水3箱/天, 食物4箱/天
     高温天气: 水9箱/天, 食物9箱/天
     沙暴天气: 水10箱/天, 食物10箱/天
     α = 2.0           : 移动消耗倍数
     β = 1.5           : 挖矿消耗倍数

   天气参数 (未知):
     weather_t ∈ {'晴朗','高温','沙暴'} : 第t天天气状况 (仅知当天)
     概率分布: 需要基于历史数据估算

⚖️ 约束条件:
   1. 初始条件约束:
      l_1^0 = 1                    (初始位置在起点)
      l_i^0 = 0, ∀i ≠ 1           (其他位置为0)
      M_0 = 10000 - P_w·w_0 - P_f·f_0    (初始资金扣除购买成本)

   2. 位置唯一性约束:
      Σ_i l_i^t = 1, ∀t ∈ {0,1,...,T}      (每天只能在一个位置)

   3. 移动合法性约束:
      x_ij^t = 1 ⟹ (i,j) ∈ E_adj           (只能在相邻节点间移动)
      Σ_j x_ij^t + s_i^t = l_i^{t-1}, ∀i,t  (位置连续性)
      Σ_i x_ij^t = l_j^t, ∀j,t              (到达位置确定性)

   4. 沙暴日约束:
      weather_t = '沙暴' ⟹ x_ij^t = 0, ∀i,j  (沙暴日必须停留)

   5. 资源平衡约束:
      w_t = w_{t-1} - C_w^t + B_w^t, ∀t    (水量平衡)
      f_t = f_{t-1} - C_f^t + B_f^t, ∀t    (食物平衡)
      其中:
        C_w^t = Σ_i c_w(weather_t)·(α·Σ_j x_ij^t + β·m_i^t + s_i^t)·l_i^{t-1}
        C_f^t = Σ_i c_f(weather_t)·(α·Σ_j x_ij^t + β·m_i^t + s_i^t)·l_i^{t-1}
        B_w^t = Σ_{i∈V} b_w^{i,t}         (村庄购买的水)
        B_f^t = Σ_{i∈V} b_f^{i,t}         (村庄购买的食物)

   6. 负重约束:
      w_t·w_weight + f_t·f_weight ≤ W_max, ∀t  (每天负重不超限)

   7. 资源非负约束:
      w_t ≥ 0, f_t ≥ 0, ∀t                  (资源量非负)

   8. 挖矿约束:
      m_i^t = 1 ⟹ i ∈ M ∧ l_i^t = 1        (只能在矿山挖矿)
      到达矿山当天不能挖矿

   9. 村庄购买约束:
      b_i^t > 0 ⟹ i ∈ V ∧ l_i^t = 1        (只能在村庄购买)
      M_t ≥ 2·P_w·b_w^{i,t} + 2·P_f·b_f^{i,t}  (资金充足)

   10. 终点约束:
       arrived_t = 1 ⟹ l_13^t = 1     (到达终点)
       arrived_t = 1 ⟹ arrived_{t'} = 1, ∀t' ≥ t  (到达后保持)

   11. 时间约束:
       arrived_T = 1                         (必须在截止日期前到达)

🎯 目标函数:
   最大化最终资产价值:

   max Z = M_T + 0.5·P_w·w_T + 0.5·P_f·f_T

   其中:
     M_T = M_0 - P_w·w_0 - P_f·f_0 + Σ_{t=1}^T Σ_{i∈M} R·m_i^t - Σ_{t=1}^T Σ_{i∈V} (2·P_w·b_w^{i,t} + 2·P_f·b_f^{i,t})

   目标函数组成部分:
     • M_T: 最终现金
     • 0.5·P_w·w_T: 剩余水的退回价值 (基准价格的50%)
     • 0.5·P_f·f_T: 剩余食物的退回价值 (基准价格的50%)

   收入来源:
     • 挖矿收入: Σ_{t=1}^T Σ_{i∈M} R·m_i^t

   支出项目:
     • 初始购买: P_w·w_0 + P_f·f_0
     • 村庄购买: Σ_{t=1}^T Σ_{i∈V} (2·P_w·b_w^{i,t} + 2·P_f·b_f^{i,t})

🔧 求解方法:
   主要方法: 贪心策略 + 蒙特卡洛模拟
   时间复杂度: O(N·T·K) where K是策略数量
   方法描述: 天气未知，使用保守贪心策略

   具体步骤:
     1. 生成多种策略候选 (直接路径、挖矿路径、村庄路径)
     2. 保守资源估算 (基于最坏天气情况)
     3. 策略评估和比较
     4. 选择预期收益最高的策略

   模型类型: 随机性MINLP
   变量类型: 混合整数变量 (连续变量 + 0-1变量)

📈 复杂度分析:
   问题规模:
     • 节点数: N = 13
     • 时间跨度: T = 10
     • 最大负重: W = 1200
     • 玩家数: P = 1

   变量数量估算:
     • 位置变量: N·T = 130
     • 移动变量: N²·T = 1690
     • 资源变量: 2·T = 20
     • 行动变量: 约 10
     • 总变量数: 约 1720

   约束数量估算:
     • 位置约束: N·T = 130
     • 资源约束: 4·T = 40
     • 逻辑约束: 约 20
     • 总约束数: 约 190

   计算复杂度:
     • 策略数量: O(N!) (路径枚举)
     • 评估复杂度: O(T·N) per strategy
     • 总时间复杂度: O(K·T·N) where K是考虑的策略数
     • 问题类别: 随机规划问题

   求解难度评估: 困难
   主要挑战: 不确定性处理


====================================================================================================
                                             第4关完整数学模型                                              
====================================================================================================

📋 问题描述:
   关卡特点: 单人策略关卡，5x5地图，天气未知但时间充裕
   玩家数量: 1人
   地图规模: 25个节点
   时间限制: 30天
   天气信息: 仅知当天
   矿山数量: 1个
   村庄数量: 1个

🔢 决策变量定义:
   空间变量:
     x_ij^t ∈ {0,1}  : 第t天从节点i移动到节点j (i,j ∈ {1,2,...,25})
     s_i^t ∈ {0,1}   : 第t天在节点i停留
     l_i^t ∈ {0,1}   : 第t天玩家位于节点i

   资源变量:
     w_t ∈ ℕ         : 第t天结束时的水量 (箱)
     f_t ∈ ℕ         : 第t天结束时的食物量 (箱)
     w_0, f_0 ∈ ℕ    : 初始购买的水量和食物量

   行动变量:
     m_i^t ∈ {0,1}   : 第t天在矿山i挖矿
     b_i^t ∈ ℕ       : 第t天在村庄i购买的资源量

   状态变量:
     M_t ∈ ℝ+        : 第t天结束时的资金
     arrived_t ∈ {0,1} : 第t天是否到达终点

📊 参数定义:
   地图参数:
     N = 25           : 节点总数
     T = 30            : 时间限制 (天)
     S = 1             : 起点节点
     E = 25             : 终点节点
     M = [18]        : 矿山节点集合
     V = [14]      : 村庄节点集合

   经济参数:
     M_0 = 10000        : 初始资金 (元)
     P_w = 5             : 水价格 (元/箱)
     P_f = 10            : 食物价格 (元/箱)
     R = 1000          : 挖矿基础收益 (元/天)

   物理参数:
     W_max = 1200       : 负重上限 (kg)
     w_weight = 3         : 水重量 (kg/箱)
     f_weight = 2         : 食物重量 (kg/箱)

   消耗参数:
     晴朗天气: 水3箱/天, 食物4箱/天
     高温天气: 水9箱/天, 食物9箱/天
     沙暴天气: 水10箱/天, 食物10箱/天
     α = 2.0           : 移动消耗倍数
     β = 1.5           : 挖矿消耗倍数

   天气参数 (未知):
     weather_t ∈ {'晴朗','高温','沙暴'} : 第t天天气状况 (仅知当天)
     概率分布: 需要基于历史数据估算

⚖️ 约束条件:
   1. 初始条件约束:
      l_1^0 = 1                    (初始位置在起点)
      l_i^0 = 0, ∀i ≠ 1           (其他位置为0)
      M_0 = 10000 - P_w·w_0 - P_f·f_0    (初始资金扣除购买成本)

   2. 位置唯一性约束:
      Σ_i l_i^t = 1, ∀t ∈ {0,1,...,T}      (每天只能在一个位置)

   3. 移动合法性约束:
      x_ij^t = 1 ⟹ (i,j) ∈ E_adj           (只能在相邻节点间移动)
      Σ_j x_ij^t + s_i^t = l_i^{t-1}, ∀i,t  (位置连续性)
      Σ_i x_ij^t = l_j^t, ∀j,t              (到达位置确定性)

   4. 沙暴日约束:
      weather_t = '沙暴' ⟹ x_ij^t = 0, ∀i,j  (沙暴日必须停留)

   5. 资源平衡约束:
      w_t = w_{t-1} - C_w^t + B_w^t, ∀t    (水量平衡)
      f_t = f_{t-1} - C_f^t + B_f^t, ∀t    (食物平衡)
      其中:
        C_w^t = Σ_i c_w(weather_t)·(α·Σ_j x_ij^t + β·m_i^t + s_i^t)·l_i^{t-1}
        C_f^t = Σ_i c_f(weather_t)·(α·Σ_j x_ij^t + β·m_i^t + s_i^t)·l_i^{t-1}
        B_w^t = Σ_{i∈V} b_w^{i,t}         (村庄购买的水)
        B_f^t = Σ_{i∈V} b_f^{i,t}         (村庄购买的食物)

   6. 负重约束:
      w_t·w_weight + f_t·f_weight ≤ W_max, ∀t  (每天负重不超限)

   7. 资源非负约束:
      w_t ≥ 0, f_t ≥ 0, ∀t                  (资源量非负)

   8. 挖矿约束:
      m_i^t = 1 ⟹ i ∈ M ∧ l_i^t = 1        (只能在矿山挖矿)
      到达矿山当天不能挖矿

   9. 村庄购买约束:
      b_i^t > 0 ⟹ i ∈ V ∧ l_i^t = 1        (只能在村庄购买)
      M_t ≥ 2·P_w·b_w^{i,t} + 2·P_f·b_f^{i,t}  (资金充足)

   10. 终点约束:
       arrived_t = 1 ⟹ l_25^t = 1     (到达终点)
       arrived_t = 1 ⟹ arrived_{t'} = 1, ∀t' ≥ t  (到达后保持)

   11. 时间约束:
       arrived_T = 1                         (必须在截止日期前到达)

🎯 目标函数:
   最大化最终资产价值:

   max Z = M_T + 0.5·P_w·w_T + 0.5·P_f·f_T

   其中:
     M_T = M_0 - P_w·w_0 - P_f·f_0 + Σ_{t=1}^T Σ_{i∈M} R·m_i^t - Σ_{t=1}^T Σ_{i∈V} (2·P_w·b_w^{i,t} + 2·P_f·b_f^{i,t})

   目标函数组成部分:
     • M_T: 最终现金
     • 0.5·P_w·w_T: 剩余水的退回价值 (基准价格的50%)
     • 0.5·P_f·f_T: 剩余食物的退回价值 (基准价格的50%)

   收入来源:
     • 挖矿收入: Σ_{t=1}^T Σ_{i∈M} R·m_i^t

   支出项目:
     • 初始购买: P_w·w_0 + P_f·f_0
     • 村庄购买: Σ_{t=1}^T Σ_{i∈V} (2·P_w·b_w^{i,t} + 2·P_f·b_f^{i,t})

🔧 求解方法:
   主要方法: 贪心策略 + 蒙特卡洛模拟
   时间复杂度: O(N·T·K) where K是策略数量
   方法描述: 天气未知，使用保守贪心策略

   具体步骤:
     1. 生成多种策略候选 (直接路径、挖矿路径、村庄路径)
     2. 保守资源估算 (基于最坏天气情况)
     3. 策略评估和比较
     4. 选择预期收益最高的策略

   模型类型: 随机性MINLP
   变量类型: 混合整数变量 (连续变量 + 0-1变量)

📈 复杂度分析:
   问题规模:
     • 节点数: N = 25
     • 时间跨度: T = 30
     • 最大负重: W = 1200
     • 玩家数: P = 1

   变量数量估算:
     • 位置变量: N·T = 750
     • 移动变量: N²·T = 18750
     • 资源变量: 2·T = 60
     • 行动变量: 约 60
     • 总变量数: 约 18840

   约束数量估算:
     • 位置约束: N·T = 750
     • 资源约束: 4·T = 120
     • 逻辑约束: 约 60
     • 总约束数: 约 930

   计算复杂度:
     • 策略数量: O(N!) (路径枚举)
     • 评估复杂度: O(T·N) per strategy
     • 总时间复杂度: O(K·T·N) where K是考虑的策略数
     • 问题类别: 随机规划问题

   求解难度评估: 困难
   主要挑战: 不确定性处理


====================================================================================================
                                             第5关完整数学模型                                              
====================================================================================================

📋 问题描述:
   关卡特点: 双人博弈关卡，天气已知，竞争环境下的策略选择
   玩家数量: 2人
   地图规模: 13个节点
   时间限制: 10天
   天气信息: 完全已知
   矿山数量: 1个
   村庄数量: 0个

🔢 决策变量定义:
   空间变量:
     x_ij^t ∈ {0,1}  : 第t天从节点i移动到节点j (i,j ∈ {1,2,...,13})
     s_i^t ∈ {0,1}   : 第t天在节点i停留
     l_i^t ∈ {0,1}   : 第t天玩家位于节点i

   资源变量:
     w_t ∈ ℕ         : 第t天结束时的水量 (箱)
     f_t ∈ ℕ         : 第t天结束时的食物量 (箱)
     w_0, f_0 ∈ ℕ    : 初始购买的水量和食物量

   行动变量:
     m_i^t ∈ {0,1}   : 第t天在矿山i挖矿
     b_i^t ∈ ℕ       : 第t天在村庄i购买的资源量

   状态变量:
     M_t ∈ ℝ+        : 第t天结束时的资金
     arrived_t ∈ {0,1} : 第t天是否到达终点

📊 参数定义:
   地图参数:
     N = 13           : 节点总数
     T = 10            : 时间限制 (天)
     S = 1             : 起点节点
     E = 13             : 终点节点
     M = [9]        : 矿山节点集合
     V = []      : 村庄节点集合

   经济参数:
     M_0 = 10000        : 初始资金 (元)
     P_w = 5             : 水价格 (元/箱)
     P_f = 10            : 食物价格 (元/箱)
     R = 200          : 挖矿基础收益 (元/天)

   物理参数:
     W_max = 1200       : 负重上限 (kg)
     w_weight = 3         : 水重量 (kg/箱)
     f_weight = 2         : 食物重量 (kg/箱)

   消耗参数:
     晴朗天气: 水3箱/天, 食物4箱/天
     高温天气: 水9箱/天, 食物9箱/天
     沙暴天气: 水10箱/天, 食物10箱/天
     α = 2.0           : 移动消耗倍数
     β = 1.5           : 挖矿消耗倍数

   天气参数 (已知):
     weather_t ∈ {'晴朗','高温','沙暴'} : 第t天天气状况
     天气预报: 10天完整预报

⚖️ 约束条件:
   1. 初始条件约束:
      l_1^0 = 1                    (初始位置在起点)
      l_i^0 = 0, ∀i ≠ 1           (其他位置为0)
      M_0 = 10000 - P_w·w_0 - P_f·f_0    (初始资金扣除购买成本)

   2. 位置唯一性约束:
      Σ_i l_i^t = 1, ∀t ∈ {0,1,...,T}      (每天只能在一个位置)

   3. 移动合法性约束:
      x_ij^t = 1 ⟹ (i,j) ∈ E_adj           (只能在相邻节点间移动)
      Σ_j x_ij^t + s_i^t = l_i^{t-1}, ∀i,t  (位置连续性)
      Σ_i x_ij^t = l_j^t, ∀j,t              (到达位置确定性)

   4. 沙暴日约束:
      weather_t = '沙暴' ⟹ x_ij^t = 0, ∀i,j  (沙暴日必须停留)

   5. 资源平衡约束:
      w_t = w_{t-1} - C_w^t + B_w^t, ∀t    (水量平衡)
      f_t = f_{t-1} - C_f^t + B_f^t, ∀t    (食物平衡)
      其中:
        C_w^t = Σ_i c_w(weather_t)·(α·Σ_j x_ij^t + β·m_i^t + s_i^t)·l_i^{t-1}
        C_f^t = Σ_i c_f(weather_t)·(α·Σ_j x_ij^t + β·m_i^t + s_i^t)·l_i^{t-1}
        B_w^t = Σ_{i∈V} b_w^{i,t}         (村庄购买的水)
        B_f^t = Σ_{i∈V} b_f^{i,t}         (村庄购买的食物)

   6. 负重约束:
      w_t·w_weight + f_t·f_weight ≤ W_max, ∀t  (每天负重不超限)

   7. 资源非负约束:
      w_t ≥ 0, f_t ≥ 0, ∀t                  (资源量非负)

   8. 挖矿约束:
      m_i^t = 1 ⟹ i ∈ M ∧ l_i^t = 1        (只能在矿山挖矿)
      到达矿山当天不能挖矿

   9. 村庄购买约束:
      b_i^t > 0 ⟹ i ∈ V ∧ l_i^t = 1        (只能在村庄购买)
      M_t ≥ 2·P_w·b_w^{i,t} + 2·P_f·b_f^{i,t}  (资金充足)

   10. 终点约束:
       arrived_t = 1 ⟹ l_13^t = 1     (到达终点)
       arrived_t = 1 ⟹ arrived_{t'} = 1, ∀t' ≥ t  (到达后保持)

   11. 时间约束:
       arrived_T = 1                         (必须在截止日期前到达)

🎯 目标函数:
   最大化最终资产价值:

   max Z = M_T + 0.5·P_w·w_T + 0.5·P_f·f_T

   其中:
     M_T = M_0 - P_w·w_0 - P_f·f_0 + Σ_{t=1}^T Σ_{i∈M} R·m_i^t - Σ_{t=1}^T Σ_{i∈V} (2·P_w·b_w^{i,t} + 2·P_f·b_f^{i,t})

   目标函数组成部分:
     • M_T: 最终现金
     • 0.5·P_w·w_T: 剩余水的退回价值 (基准价格的50%)
     • 0.5·P_f·f_T: 剩余食物的退回价值 (基准价格的50%)

   收入来源:
     • 挖矿收入: Σ_{t=1}^T Σ_{i∈M} R·m_i^t

   支出项目:
     • 初始购买: P_w·w_0 + P_f·f_0
     • 村庄购买: Σ_{t=1}^T Σ_{i∈V} (2·P_w·b_w^{i,t} + 2·P_f·b_f^{i,t})

🔧 求解方法:
   主要方法: 动态规划 (Dynamic Programming)
   时间复杂度: O(N²·T·W_max)
   方法描述: 天气完全已知，可以使用确定性动态规划

   具体步骤:
     1. 状态定义: dp[t][i][w][f] = 在第t天位于节点i，拥有w箱水、f箱食物时的最大资产
     2. 状态转移: 考虑所有可能的行动(移动/停留/挖矿/购买)
     3. 边界条件: dp[0][S][w_0][f_0] = M_0
     4. 目标状态: max{dp[t][E][w][f] | t ≤ T, w ≥ 0, f ≥ 0}

   模型类型: 确定性MINLP
   变量类型: 混合整数变量 (连续变量 + 0-1变量)

📈 复杂度分析:
   问题规模:
     • 节点数: N = 13
     • 时间跨度: T = 10
     • 最大负重: W = 1200
     • 玩家数: P = 2

   变量数量估算:
     • 位置变量: N·T = 130
     • 移动变量: N²·T = 1690
     • 资源变量: 2·T = 20
     • 行动变量: 约 10
     • 总变量数: 约 1720

   约束数量估算:
     • 位置约束: N·T = 130
     • 资源约束: 4·T = 40
     • 逻辑约束: 约 20
     • 总约束数: 约 190

   计算复杂度:
     • 状态空间: O(T·N·W²) ≈ O(10·13·120²)
     • 时间复杂度: O(T·N³·W²)
     • 空间复杂度: O(T·N·W²)
     • 问题类别: NP-Hard (但可用DP在伪多项式时间内求解)

   求解难度评估: 中等
   主要挑战: 状态空间爆炸


====================================================================================================
                                             第6关完整数学模型                                              
====================================================================================================

📋 问题描述:
   关卡特点: 三人博弈关卡，天气未知，多人竞争复杂环境
   玩家数量: 3人
   地图规模: 25个节点
   时间限制: 30天
   天气信息: 仅知当天
   矿山数量: 1个
   村庄数量: 1个

🔢 决策变量定义:
   空间变量:
     x_ij^t ∈ {0,1}  : 第t天从节点i移动到节点j (i,j ∈ {1,2,...,25})
     s_i^t ∈ {0,1}   : 第t天在节点i停留
     l_i^t ∈ {0,1}   : 第t天玩家位于节点i

   资源变量:
     w_t ∈ ℕ         : 第t天结束时的水量 (箱)
     f_t ∈ ℕ         : 第t天结束时的食物量 (箱)
     w_0, f_0 ∈ ℕ    : 初始购买的水量和食物量

   行动变量:
     m_i^t ∈ {0,1}   : 第t天在矿山i挖矿
     b_i^t ∈ ℕ       : 第t天在村庄i购买的资源量

   状态变量:
     M_t ∈ ℝ+        : 第t天结束时的资金
     arrived_t ∈ {0,1} : 第t天是否到达终点

📊 参数定义:
   地图参数:
     N = 25           : 节点总数
     T = 30            : 时间限制 (天)
     S = 1             : 起点节点
     E = 25             : 终点节点
     M = [18]        : 矿山节点集合
     V = [14]      : 村庄节点集合

   经济参数:
     M_0 = 10000        : 初始资金 (元)
     P_w = 5             : 水价格 (元/箱)
     P_f = 10            : 食物价格 (元/箱)
     R = 1000          : 挖矿基础收益 (元/天)

   物理参数:
     W_max = 1200       : 负重上限 (kg)
     w_weight = 3         : 水重量 (kg/箱)
     f_weight = 2         : 食物重量 (kg/箱)

   消耗参数:
     晴朗天气: 水3箱/天, 食物4箱/天
     高温天气: 水9箱/天, 食物9箱/天
     沙暴天气: 水10箱/天, 食物10箱/天
     α = 2.0           : 移动消耗倍数
     β = 1.5           : 挖矿消耗倍数

   天气参数 (未知):
     weather_t ∈ {'晴朗','高温','沙暴'} : 第t天天气状况 (仅知当天)
     概率分布: 需要基于历史数据估算

⚖️ 约束条件:
   1. 初始条件约束:
      l_1^0 = 1                    (初始位置在起点)
      l_i^0 = 0, ∀i ≠ 1           (其他位置为0)
      M_0 = 10000 - P_w·w_0 - P_f·f_0    (初始资金扣除购买成本)

   2. 位置唯一性约束:
      Σ_i l_i^t = 1, ∀t ∈ {0,1,...,T}      (每天只能在一个位置)

   3. 移动合法性约束:
      x_ij^t = 1 ⟹ (i,j) ∈ E_adj           (只能在相邻节点间移动)
      Σ_j x_ij^t + s_i^t = l_i^{t-1}, ∀i,t  (位置连续性)
      Σ_i x_ij^t = l_j^t, ∀j,t              (到达位置确定性)

   4. 沙暴日约束:
      weather_t = '沙暴' ⟹ x_ij^t = 0, ∀i,j  (沙暴日必须停留)

   5. 资源平衡约束:
      w_t = w_{t-1} - C_w^t + B_w^t, ∀t    (水量平衡)
      f_t = f_{t-1} - C_f^t + B_f^t, ∀t    (食物平衡)
      其中:
        C_w^t = Σ_i c_w(weather_t)·(α·Σ_j x_ij^t + β·m_i^t + s_i^t)·l_i^{t-1}
        C_f^t = Σ_i c_f(weather_t)·(α·Σ_j x_ij^t + β·m_i^t + s_i^t)·l_i^{t-1}
        B_w^t = Σ_{i∈V} b_w^{i,t}         (村庄购买的水)
        B_f^t = Σ_{i∈V} b_f^{i,t}         (村庄购买的食物)

   6. 负重约束:
      w_t·w_weight + f_t·f_weight ≤ W_max, ∀t  (每天负重不超限)

   7. 资源非负约束:
      w_t ≥ 0, f_t ≥ 0, ∀t                  (资源量非负)

   8. 挖矿约束:
      m_i^t = 1 ⟹ i ∈ M ∧ l_i^t = 1        (只能在矿山挖矿)
      到达矿山当天不能挖矿

   9. 村庄购买约束:
      b_i^t > 0 ⟹ i ∈ V ∧ l_i^t = 1        (只能在村庄购买)
      M_t ≥ 2·P_w·b_w^{i,t} + 2·P_f·b_f^{i,t}  (资金充足)

   10. 终点约束:
       arrived_t = 1 ⟹ l_25^t = 1     (到达终点)
       arrived_t = 1 ⟹ arrived_{t'} = 1, ∀t' ≥ t  (到达后保持)

   11. 时间约束:
       arrived_T = 1                         (必须在截止日期前到达)

🎯 目标函数:
   最大化最终资产价值:

   max Z = M_T + 0.5·P_w·w_T + 0.5·P_f·f_T

   其中:
     M_T = M_0 - P_w·w_0 - P_f·f_0 + Σ_{t=1}^T Σ_{i∈M} R·m_i^t - Σ_{t=1}^T Σ_{i∈V} (2·P_w·b_w^{i,t} + 2·P_f·b_f^{i,t})

   目标函数组成部分:
     • M_T: 最终现金
     • 0.5·P_w·w_T: 剩余水的退回价值 (基准价格的50%)
     • 0.5·P_f·f_T: 剩余食物的退回价值 (基准价格的50%)

   收入来源:
     • 挖矿收入: Σ_{t=1}^T Σ_{i∈M} R·m_i^t

   支出项目:
     • 初始购买: P_w·w_0 + P_f·f_0
     • 村庄购买: Σ_{t=1}^T Σ_{i∈V} (2·P_w·b_w^{i,t} + 2·P_f·b_f^{i,t})

🔧 求解方法:
   主要方法: 贪心策略 + 蒙特卡洛模拟
   时间复杂度: O(N·T·K) where K是策略数量
   方法描述: 天气未知，使用保守贪心策略

   具体步骤:
     1. 生成多种策略候选 (直接路径、挖矿路径、村庄路径)
     2. 保守资源估算 (基于最坏天气情况)
     3. 策略评估和比较
     4. 选择预期收益最高的策略

   模型类型: 随机性MINLP
   变量类型: 混合整数变量 (连续变量 + 0-1变量)

📈 复杂度分析:
   问题规模:
     • 节点数: N = 25
     • 时间跨度: T = 30
     • 最大负重: W = 1200
     • 玩家数: P = 3

   变量数量估算:
     • 位置变量: N·T = 750
     • 移动变量: N²·T = 18750
     • 资源变量: 2·T = 60
     • 行动变量: 约 60
     • 总变量数: 约 18840

   约束数量估算:
     • 位置约束: N·T = 750
     • 资源约束: 4·T = 120
     • 逻辑约束: 约 60
     • 总约束数: 约 930

   计算复杂度:
     • 策略数量: O(N!) (路径枚举)
     • 评估复杂度: O(T·N) per strategy
     • 总时间复杂度: O(K·T·N) where K是考虑的策略数
     • 问题类别: 随机规划问题

   求解难度评估: 困难
   主要挑战: 不确定性处理


========================================================================================================================
                                                         数学建模总结                                                         
========================================================================================================================

📊 模型对比分析:

┌─────────┬──────────────┬──────────────┬──────────────┬──────────────┐
│  关卡   │   问题类型   │   求解方法   │   复杂度     │   主要挑战   │
├─────────┼──────────────┼──────────────┼──────────────┼──────────────┤
│  第1关  │  确定性MINLP │   动态规划   │    O(N²T)    │   路径优化   │
│  第2关  │  确定性MINLP │   动态规划   │   O(N²TW)    │  多目标优化  │
│  第3关  │  随机性MINLP │   贪心策略   │   O(KTN)     │  不确定性    │
│  第4关  │  随机性MINLP │   贪心策略   │   O(KTN)     │  状态空间大  │
│  第5关  │  博弈论模型  │   动态规划   │   O(N²TP)    │  多人博弈    │
│  第6关  │  随机博弈    │   贪心策略   │   O(KTNP)    │ 随机+博弈    │
└─────────┴──────────────┴──────────────┴──────────────┴──────────────┘

🎯 关键数学概念:

1. **混合整数非线性规划 (MINLP)**:
   • 连续变量: 资源量、资金
   • 整数变量: 位置、行动决策
   • 非线性: 资源消耗与天气、行动的乘积关系

2. **动态规划状态转移方程**:
   dp[t][i][w][f] = max{
     dp[t-1][j][w'][f'] + reward - cost
     | (j,i) ∈ adjacent, resource_constraint_satisfied
   }

3. **随机规划模型**:
   E[Z] = Σ_ω P(ω) · Z(ω)
   其中 ω 表示天气场景，P(ω) 是概率

4. **博弈论纳什均衡**:
   对于多人关卡，寻找策略组合 (s₁*, s₂*, ..., sₙ*) 使得:
   πᵢ(sᵢ*, s₋ᵢ*) ≥ πᵢ(sᵢ, s₋ᵢ*), ∀sᵢ, ∀i

🔧 求解算法总结:

• **确定性关卡 (1,2,5)**:
  - 使用动态规划精确求解
  - 状态空间: 时间×位置×资源状态
  - 保证全局最优解

• **随机性关卡 (3,4,6)**:
  - 使用保守贪心策略
  - 基于最坏情况的资源估算
  - 多策略比较选择

💡 模型创新点:

1. **多层次决策结构**:
   • 战略层: 路径选择 (direct/mining/village)
   • 战术层: 资源分配和时间安排
   • 操作层: 每日具体行动

2. **不确定性处理**:
   • 天气已知: 完全信息动态规划
   • 天气未知: 鲁棒优化 + 保守策略

3. **多目标权衡**:
   • 时间效率 vs 收益最大化
   • 风险控制 vs 收益追求
   • 资源消耗 vs 安全边际

📈 计算复杂度分析:

• **最优解复杂度**: 所有关卡都是NP-Hard问题
• **实际求解**: 通过启发式算法在合理时间内获得高质量解
• **可扩展性**: 算法可扩展到更大规模的地图和更多玩家

🚀 实际应用价值:

1. **物流优化**: 路径规划、资源调度
2. **项目管理**: 时间-成本-质量权衡
3. **风险管理**: 不确定环境下的决策
4. **博弈分析**: 竞争环境下的策略选择

========================================================================================================================
完整数学建模报告生成完成 - 2025-08-01 21:06:20
========================================================================================================================