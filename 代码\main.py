#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正版主程序
严格按照原题规则求解穿越沙漠问题
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from config.game_config import GameConfig
    from solvers.level_solver import CorrectedLevelSolver
    from utils.result_exporter import ResultExporter
except ImportError as e:
    print(f"导入模块失败: {e}")
    exit(1)

class DesertCrossingGame:
    """修正版穿越沙漠游戏"""
    
    def __init__(self):
        self.config = GameConfig()
        self.result_exporter = ResultExporter()
        
    def solve_level(self, level: int):
        """求解指定关卡"""
        print(f"\n{'='*60}")
        print(f"修正版求解器 - 第{level}关")
        print(f"{'='*60}")
        
        # 获取关卡配置
        level_config = self.config.get_level_config(level)
        if not level_config:
            print(f"错误：未找到第{level}关的配置")
            return None
        
        # 显示关卡规则
        self._display_level_rules(level, level_config)
        
        # 创建修正版求解器
        solver = CorrectedLevelSolver(level_config)
        
        # 求解
        result = solver.solve()
        
        if result and result.is_feasible:
            self._display_result(result)
            self._export_result(result)
        else:
            print("❌ 求解失败：未找到可行解")
            
        return result
    
    def _display_level_rules(self, level: int, config):
        """显示关卡规则"""
        print(f"\n📋 第{level}关规则:")
        print(f"   玩家数量: {config.players}人")
        print(f"   负重上限: {config.max_weight}kg")
        print(f"   时间限制: {config.deadline_days}天")
        print(f"   初始资金: {config.initial_money}元")
        
        # 天气信息规则
        if level in [1, 2, 5]:
            print(f"   天气信息: 完全已知（{len(config.weather_forecast)}天预报）")
        elif level in [3, 4, 6]:
            print(f"   天气信息: 仅知当天天气")
        
        # 特殊规则
        print(f"   🚨 沙暴日必须停留（不能移动）")
        print(f"   ⛏️  矿山是可选的（不强制访问）")
        print(f"   🏪 村庄可补给资源（价格2倍）")
        
        if config.village_nodes:
            print(f"   村庄位置: {config.village_nodes}")
        if config.mine_nodes:
            print(f"   矿山位置: {config.mine_nodes}")
    
    def _display_result(self, result):
        """显示求解结果"""
        print(f"\n✅ 求解成功！")
        print(f"   策略类型: {result.strategy_type}")
        print(f"   最终资金: {result.final_money:,}元")
        print(f"   净利润: {result.final_money - 10000:,}元")
        print(f"   总用时: {result.final_day}天")
        print(f"   路径: {' → '.join(map(str, result.path))}")
        
        # 显示关键统计
        total_income = sum(log.get('income', 0) for log in result.daily_log)
        total_water_consumed = sum(log.get('water_consumed', 0) for log in result.daily_log)
        total_food_consumed = sum(log.get('food_consumed', 0) for log in result.daily_log)
        
        print(f"   总收入: {total_income:,}元")
        print(f"   总消耗: 水{total_water_consumed}箱, 食物{total_food_consumed}箱")
        
        # 显示沙暴日处理
        storm_days = [log for log in result.daily_log if log.get('weather') == '沙暴']
        if storm_days:
            print(f"   沙暴日: {len(storm_days)}天（均在原地停留）")
    
    def _export_result(self, result):
        """导出结果"""
        output_dir = Path("results")
        output_dir.mkdir(exist_ok=True)

        # 导出每日日志
        daily_log_file = output_dir / f"level_{result.level}_daily_log.csv"
        self.result_exporter.export_daily_log(result.daily_log, daily_log_file)
        
        # 导出汇总结果
        summary_data = {
            'level': result.level,
            'strategy_type': result.strategy_type,
            'final_money': result.final_money,
            'net_profit': result.final_money - 10000,
            'final_day': result.final_day,
            'path': result.path,
            'is_feasible': result.is_feasible
        }
        
        import json
        summary_file = output_dir / f"level_{result.level}_summary.json"
        with open(summary_file, 'w', encoding='utf-8') as f:
            json.dump(summary_data, f, ensure_ascii=False, indent=2)
        
        print(f"   📁 结果已导出到: {output_dir}")

def main():
    """主函数"""
    print("🏜️  穿越沙漠数学建模问题求解器")
    print("严格按照原题规则实现")
    print("="*60)

    game = DesertCrossingGame()

    print("\n📝 核心规则:")
    print("1. ✅ 矿山访问可选（不强制访问）")
    print("2. ✅ 沙暴日强制停留（不能移动）")
    print("3. ✅ 正确处理天气信息获取规则")
    print("4. ✅ 支持多种策略（直接/挖矿/村庄）")

    print("\n请选择求解模式:")
    print("1. 求解单个关卡")
    print("2. 求解所有关卡")

    try:
        choice = input("\n请输入选择 (1-2): ").strip()
        
        if choice == "1":
            level = int(input("请输入关卡号 (1-6): "))
            if 1 <= level <= 6:
                game.solve_level(level)
            else:
                print("错误：关卡号必须在1-6之间")
                
        elif choice == "2":
            print("\n开始求解所有关卡...")
            results = {}

            for level in range(1, 7):  # 求解所有6关
                result = game.solve_level(level)
                if result:
                    results[level] = result
            
            # 显示汇总
            print(f"\n{'='*60}")
            print("所有关卡求解汇总")
            print(f"{'='*60}")
            
            total_profit = 0
            for level in sorted(results.keys()):
                result = results[level]
                profit = result.final_money - 10000
                total_profit += profit
                print(f"第{level}关: {result.strategy_type:8} 策略, "
                      f"最终资金 {result.final_money:,}元, "
                      f"净利润 {profit:,}元")
            
            print(f"\n总净利润: {total_profit:,}元")
            print(f"平均净利润: {total_profit/len(results):,.0f}元")
            
        else:
            print("错误：无效的选择")
            
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
