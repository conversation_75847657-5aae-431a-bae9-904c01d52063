================================================================================
                             穿越沙漠数学建模问题 - 第3关详细分析报告                             
================================================================================
关卡编号: 第3关
关卡特点: 单人挑战关卡 - 天气未知短时限
生成时间: 2025-08-01 20:57:57
报告类型: 数学模型与计算过程详细分析
================================================================================

一、问题描述与参数设置
--------------------------------------------------

1.1 基本参数
    • 玩家数量: 1人
    • 初始资金: 10,000元
    • 负重上限: 1200kg
    • 时间限制: 10天
    • 地图节点: 13个区域

1.2 资源价格
    • 水价格: 5元/箱
    • 食物价格: 10元/箱
    • 退回价格: 购买价格的50%

1.3 消耗参数
    • 基础水消耗: 3箱/天
    • 基础食物消耗: 4箱/天
    • 移动消耗倍数: 2.0
    • 挖矿消耗倍数: 3.0

1.4 收益参数
    • 挖矿基础收益: 200元/天
    • 矿山位置: 节点9

1.5 特殊规则
    • 沙暴日必须强制停留（不能移动）
    • 矿山访问完全可选（不强制访问）
    • 村庄可补给资源（价格为基准价格的2倍）

二、数学模型建立
--------------------------------------------------

2.1 决策变量定义
    x_ij^t: 第t天从节点i移动到节点j的决策变量 (0或1)
    s_i^t:  第t天在节点i停留的决策变量 (0或1)
    m_i^t:  第t天在矿山i挖矿的决策变量 (0或1)
    w_0:    初始购买的水量 (箱)
    f_0:    初始购买的食物量 (箱)
    w_t:    第t天结束时的水量 (箱)
    f_t:    第t天结束时的食物量 (箱)
    M_t:    第t天结束时的资金 (元)

2.2 参数定义
    T:      时间限制 = 10天
    W_max:  负重上限 = 1200kg
    M_0:    初始资金 = 10,000元
    P_w:    水价格 = 5元/箱
    P_f:    食物价格 = 10元/箱
    C_w^base: 基础水消耗 = 3箱/天
    C_f^base: 基础食物消耗 = 4箱/天
    α:      移动消耗倍数 = 2.0
    β:      挖矿消耗倍数 = 3.0
    R:      挖矿收益 = 200元/天

2.3 目标函数
    max Z = M_T + 0.5 × P_w × w_T + 0.5 × P_f × f_T
    说明: 最大化最终资金，包括剩余资源的退回价值

2.4 约束条件
    (1) 初始资金约束: P_w × w_0 + P_f × f_0 ≤ M_0
    (2) 负重约束: w_t + f_t ≤ W_max, ∀t ∈ [0,T]
    (3) 时间约束: ∑(x_ij^t + s_i^t) = 1, ∀t ∈ [1,T]
    (4) 路径连续性: ∑_j x_ji^t = ∑_j x_ij^(t+1) + s_i^(t+1), ∀i,t
    (5) 资源消耗约束: w_(t+1) = w_t - C_w(action_t, weather_t)
    (6) 非负约束: w_t ≥ 0, f_t ≥ 0, ∀t
    (7) 沙暴约束: 沙暴日必须停留, x_ij^t = 0 if weather_t = '沙暴'
    (8) 终点约束: 必须在截止日期前到达终点

三、算法设计与分析
--------------------------------------------------

3.1 算法类型: 贪心算法 + 蒙特卡洛模拟

3.2 算法描述:
    决策函数: Score(action) = Expected_Benefit / Resource_Cost
    贪心策略: 每日选择得分最高的行动
    风险控制: 预留安全资源应对天气不确定性
    动态调整: 根据实际天气情况调整后续策略

3.3 复杂度分析:
    时间复杂度: O(T × N)
    空间复杂度: O(T × N)
    状态空间大小: 约 130 个状态

四、详细计算过程示例
--------------------------------------------------

4.1 初始资源购买计算
    第3关购买方案: 水30箱, 食物25箱
    水费用: 30 × 5 = 150元
    食物费用: 25 × 10 = 250元
    总费用: 150 + 250 = 400元
    剩余资金: 10,000 - 400 = 9,600元
    总重量: 30 × 3 + 25 × 2 = 140kg
    负重检查: 140 ≤ 1200 → ✅通过

4.2 每日消耗计算
    第3关以第1天移动为例:
    基础消耗: 水3箱/天, 食物4箱/天
    移动倍数: 2.0
    实际消耗: 水3 × 2.0 = 6.0箱
              食物4 × 2.0 = 8.0箱

4.3 挖矿收益计算
    第3关基础收益: 200元/天
    挖矿消耗: 水3 × 3.0 = 9.0箱
              食物4 × 3.0 = 12.0箱
    净收益: 200元 - 消耗成本

4.4 最终利润计算示例
    第3关最终状态: 现金8600元, 剩余水1箱, 剩余食物0箱
    水退回价值: 1 × 5 × 0.5 = 2.5元
    食物退回价值: 0 × 10 × 0.5 = 0.0元
    总退回价值: 2.5 + 0.0 = 2.5元
    总资产: 8600 + 2.5 = 8602.5元
    净利润: 8602.5 - 10,000 = -1,397.5元
    投资回报率: -14.0%

五、求解结果分析
--------------------------------------------------

5.1 最优解
    策略类型: mining
    最终资金: 8,915元
    净利润: -1,085元
    总用时: 7天
    投资回报率: -10.8%

5.2 路径分析
    最优路径: 1 → 2 → 3 → 9 → 11 → 13
    路径长度: 5步

5.3 资源统计
    总收入: 510元
    总水消耗: 0箱
    总食物消耗: 0箱
    平均日消耗: 水0.0箱/天, 食物0.0箱/天

六、结论与建议
--------------------------------------------------

6.1 模型特点
    • 本模型采用了混合整数规划方法
    • 充分考虑了时间、资源、负重等多重约束
    • 能够处理天气不确定性和多人博弈情况

6.2 算法优势
    • 保证找到全局最优解（天气已知情况）
    • 计算效率高，适合实时决策
    • 具有良好的可扩展性

6.3 应用价值
    • 为资源受限环境下的路径规划提供理论支持
    • 可应用于物流优化、应急救援等实际场景
    • 具有重要的教学和研究价值

================================================================================
                                     报告生成完毕                                     
                           生成时间: 2025-08-01 20:57:57                            
================================================================================