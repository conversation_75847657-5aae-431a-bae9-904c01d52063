#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
快速运行数学模型演示
展示穿越沙漠问题的完整数学建模过程
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

def run_quick_demo():
    """快速演示第1关的完整数学建模过程"""
    print("🚀 穿越沙漠数学建模快速演示")
    print("=" * 60)
    print("演示内容: 生成第1关详细分析报告")
    print("=" * 60)

    try:
        from mathematical_model_demo import MathematicalModelDemo

        demo = MathematicalModelDemo()

        # 运行第1关的完整演示
        result = demo.run_complete_demo(1)

        print("\n" + "="*60)
        print("🎉 演示完成！")
        print("📄 详细分析报告已保存为 .txt 文件")
        print("📁 数学模型已保存为 .json 文件")
        print("💡 请查看 results/ 目录中的文件")

    except ImportError as e:
        print(f"❌ 导入模块失败: {e}")
        print("请确保所有依赖模块都已正确安装")
    except Exception as e:
        print(f"❌ 运行出错: {e}")
        import traceback
        traceback.print_exc()

def run_all_levels_demo():
    """运行所有关卡的分析"""
    print("🌟 所有关卡数学建模分析")
    print("=" * 60)

    try:
        from mathematical_model_demo import MathematicalModelDemo

        demo = MathematicalModelDemo()

        # 运行所有关卡的分析
        demo.run_all_levels_demo()

        print("\n🎉 所有关卡分析完成！")
        print("📄 每个关卡的详细报告都已保存为 .txt 文件")
        print("📊 汇总分析报告已生成")

    except Exception as e:
        print(f"❌ 运行出错: {e}")
        import traceback
        traceback.print_exc()

def view_generated_reports():
    """查看已生成的报告"""
    print("📁 查看生成的报告文件")
    print("=" * 60)

    try:
        from pathlib import Path

        results_dir = Path("results")
        if not results_dir.exists():
            print("❌ results 目录不存在，请先运行分析程序")
            return

        # 查找报告文件
        txt_files = list(results_dir.glob("*.txt"))
        json_files = list(results_dir.glob("*.json"))

        if txt_files:
            print("📄 详细分析报告 (.txt):")
            for file in sorted(txt_files):
                print(f"   • {file.name}")

        if json_files:
            print("\n📊 数学模型文档 (.json):")
            for file in sorted(json_files):
                print(f"   • {file.name}")

        if not txt_files and not json_files:
            print("❌ 未找到报告文件，请先运行分析程序")
        else:
            print(f"\n📁 所有文件位于: {results_dir.absolute()}")

    except Exception as e:
        print(f"❌ 查看文件出错: {e}")

def main():
    """主函数"""
    print("🎓 穿越沙漠数学建模演示系统")
    print("=" * 60)
    print("选择操作模式:")
    print("1. 🚀 生成第1关详细报告 (推荐)")
    print("2. 🌟 生成所有关卡报告")
    print("3. 📁 查看已生成的报告")
    print("4. 🔄 运行原始求解器")
    
    try:
        choice = input("\n请输入选择 (1-4): ").strip()
        
        if choice == "1":
            run_quick_demo()
        elif choice == "2":
            run_all_levels_demo()
        elif choice == "3":
            view_generated_reports()
        elif choice == "4":
            print("🔄 启动原始求解器...")
            os.system("python3 main.py")
        else:
            print("❌ 无效选择，运行第1关演示...")
            run_quick_demo()
            
    except KeyboardInterrupt:
        print("\n\n👋 程序被用户中断")
    except Exception as e:
        print(f"\n❌ 程序执行出错: {e}")

if __name__ == "__main__":
    main()
