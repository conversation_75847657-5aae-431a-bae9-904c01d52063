#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据验证分析 - 检查统计图表的数据准确性
"""

import json
import pandas as pd
from pathlib import Path

def verify_data_accuracy():
    """验证数据准确性"""
    print("="*60)
    print("数据验证分析报告")
    print("="*60)
    
    results_dir = Path('results')
    
    # 读取汇总报告
    summary_report_file = results_dir / 'all_levels_summary_report.txt'
    if summary_report_file.exists():
        print("\n📄 汇总报告数据:")
        with open(summary_report_file, 'r', encoding='utf-8') as f:
            content = f.read()
            print(content)
    
    print("\n" + "="*60)
    print("📊 JSON文件详细数据验证:")
    print("="*60)
    
    # 验证每个关卡的JSON数据
    total_profit_json = 0
    for level in range(1, 7):
        summary_file = results_dir / f'level_{level}_summary.json'
        if summary_file.exists():
            with open(summary_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
                
            strategy = data.get('strategy_type', 'N/A')
            final_money = data.get('final_money', 0)
            net_profit = data.get('net_profit', 0)
            days = data.get('final_day', 0)
            
            total_profit_json += net_profit
            
            print(f"关卡{level}: {strategy}策略, 最终资金{final_money:,}, 净利润{net_profit:,}, 用时{days}天")
    
    print(f"\nJSON文件计算的总净利润: {total_profit_json:,}元")
    
    print("\n" + "="*60)
    print("🔍 数据一致性检查:")
    print("="*60)
    
    # 检查数据一致性
    print("1. 汇总报告 vs JSON文件数据对比:")
    print("   - 汇总报告显示总净利润: 33,020元")
    print(f"   - JSON文件计算总净利润: {total_profit_json:,}元")
    
    if abs(33020 - total_profit_json) < 10:
        print("   ✅ 数据基本一致")
    else:
        print("   ❌ 数据存在差异，需要进一步检查")
    
    print("\n2. 净利润计算逻辑检查:")
    print("   净利润 = 最终资金 - 初始资金(10,000元)")
    
    for level in range(1, 7):
        summary_file = results_dir / f'level_{level}_summary.json'
        if summary_file.exists():
            with open(summary_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            final_money = data.get('final_money', 0)
            net_profit = data.get('net_profit', 0)
            calculated_profit = final_money - 10000
            
            if abs(net_profit - calculated_profit) < 1:
                status = "✅"
            else:
                status = "❌"
            
            print(f"   关卡{level}: 记录净利润{net_profit:,}, 计算净利润{calculated_profit:,} {status}")
    
    print("\n3. 策略分布统计:")
    strategy_count = {}
    for level in range(1, 7):
        summary_file = results_dir / f'level_{level}_summary.json'
        if summary_file.exists():
            with open(summary_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            strategy = data.get('strategy_type', 'unknown')
            strategy_count[strategy] = strategy_count.get(strategy, 0) + 1
    
    for strategy, count in strategy_count.items():
        print(f"   {strategy}策略: {count}次")
    
    print("\n" + "="*60)
    print("💡 问题分析和建议:")
    print("="*60)
    
    print("1. 数据准确性:")
    print("   - 需要确保所有图表使用相同的数据源")
    print("   - 建议直接从JSON文件读取数据，而不是手工输入")
    
    print("\n2. 计算逻辑:")
    print("   - 净利润计算逻辑需要统一")
    print("   - 效率指标(元/天)计算需要验证")
    
    print("\n3. 图表改进建议:")
    print("   - 添加数据来源说明")
    print("   - 增加数据验证步骤")
    print("   - 提供原始数据表格对照")
    
    print("\n4. 可视化改进:")
    print("   - 在图表中显示具体数值")
    print("   - 添加数据标签和说明")
    print("   - 提供数据验证报告")

def create_corrected_summary():
    """创建修正后的数据汇总"""
    print("\n" + "="*60)
    print("📋 修正后的数据汇总表:")
    print("="*60)
    
    results_dir = Path('results')
    
    print("| 关卡 | 策略类型 | 最终资金 | 净利润 | 用时 | 效率(元/天) | 状态 |")
    print("|------|----------|----------|--------|------|-------------|------|")
    
    total_profit = 0
    for level in range(1, 7):
        summary_file = results_dir / f'level_{level}_summary.json'
        if summary_file.exists():
            with open(summary_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            
            strategy = data.get('strategy_type', 'N/A')
            final_money = data.get('final_money', 0)
            net_profit = data.get('net_profit', 0)
            days = data.get('final_day', 1)
            
            efficiency = net_profit / days if days > 0 else 0
            status = "✅盈利" if net_profit > 0 else "❌亏损"
            
            total_profit += net_profit
            
            print(f"| {level} | {strategy} | {final_money:,} | {net_profit:,} | {days} | {efficiency:.0f} | {status} |")
    
    print(f"\n总净利润: {total_profit:,}元")
    print(f"平均净利润: {total_profit/6:.0f}元")
    
    profitable_count = 0
    for level in range(1, 7):
        summary_file = results_dir / f'level_{level}_summary.json'
        if summary_file.exists():
            with open(summary_file, 'r', encoding='utf-8') as f:
                data = json.load(f)
            if data.get('net_profit', 0) > 0:
                profitable_count += 1
    
    print(f"盈利成功率: {profitable_count/6*100:.1f}%")

if __name__ == "__main__":
    verify_data_accuracy()
    create_corrected_summary()
