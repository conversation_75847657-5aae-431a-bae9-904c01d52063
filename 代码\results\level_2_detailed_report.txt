================================================================================
                             穿越沙漠数学建模问题 - 第2关详细分析报告                             
================================================================================
关卡编号: 第2关
关卡特点: 单人复杂关卡 - 多矿山多村庄
生成时间: 2025-08-02 12:38:45
报告类型: 数学模型与计算过程详细分析
================================================================================

一、问题描述与参数设置
--------------------------------------------------

1.1 基本参数
    • 玩家数量: 1人
    • 初始资金: 10,000元
    • 负重上限: 1200kg
    • 时间限制: 30天
    • 地图节点: 64个区域

1.2 资源价格
    • 水价格: 5元/箱
    • 食物价格: 10元/箱
    • 退回价格: 购买价格的50%

1.3 消耗参数
    • 基础水消耗: 5箱/天
    • 基础食物消耗: 7箱/天
    • 移动消耗倍数: 2.0
    • 挖矿消耗倍数: 3.0

1.4 收益参数
    • 挖矿基础收益: 1,000元/天
    • 矿山位置: 节点30

1.5 特殊规则
    • 沙暴日必须强制停留（不能移动）
    • 矿山访问完全可选（不强制访问）
    • 村庄可补给资源（价格为基准价格的2倍）

二、数学模型建立
--------------------------------------------------

2.1 决策变量定义
    x_ij^t: 第t天从节点i移动到节点j的决策变量 (0或1)
    s_i^t:  第t天在节点i停留的决策变量 (0或1)
    m_i^t:  第t天在矿山i挖矿的决策变量 (0或1)
    w_0:    初始购买的水量 (箱)
    f_0:    初始购买的食物量 (箱)
    w_t:    第t天结束时的水量 (箱)
    f_t:    第t天结束时的食物量 (箱)
    M_t:    第t天结束时的资金 (元)

2.2 参数定义
    T:      时间限制 = 30天
    W_max:  负重上限 = 1200kg
    M_0:    初始资金 = 10,000元
    P_w:    水价格 = 5元/箱
    P_f:    食物价格 = 10元/箱
    C_w^base: 基础水消耗 = 5箱/天
    C_f^base: 基础食物消耗 = 7箱/天
    α:      移动消耗倍数 = 2.0
    β:      挖矿消耗倍数 = 3.0
    R:      挖矿收益 = 1,000元/天

2.3 目标函数
    max Z = M_T + 0.5 × P_w × w_T + 0.5 × P_f × f_T
    说明: 最大化最终资金，包括剩余资源的退回价值

2.4 约束条件
    (1) 初始资金约束: P_w × w_0 + P_f × f_0 ≤ M_0
    (2) 负重约束: w_t + f_t ≤ W_max, ∀t ∈ [0,T]
    (3) 时间约束: ∑(x_ij^t + s_i^t) = 1, ∀t ∈ [1,T]
    (4) 路径连续性: ∑_j x_ji^t = ∑_j x_ij^(t+1) + s_i^(t+1), ∀i,t
    (5) 资源消耗约束: w_(t+1) = w_t - C_w(action_t, weather_t)
    (6) 非负约束: w_t ≥ 0, f_t ≥ 0, ∀t
    (7) 沙暴约束: 沙暴日必须停留, x_ij^t = 0 if weather_t = '沙暴'
    (8) 终点约束: 必须在截止日期前到达终点

三、算法设计与分析
--------------------------------------------------

3.1 算法类型: 动态规划算法

3.2 算法描述:
    状态定义: State(t, i, w, f, m)
    状态转移方程:
    V(t, i, w, f, m) = max{
        V(t+1, i, w-C_w^stay, f-C_f^stay, m+income),  // 停留
        max_j V(t+1, j, w-C_w^move, f-C_f^move, m),   // 移动
        V(t+1, i, w-C_w^mine, f-C_f^mine, m+R)       // 挖矿
    }
    最优子结构: 每个状态的最优解由子状态的最优解决定

3.3 复杂度分析:
    时间复杂度: O(T × N × W × F × M)
    空间复杂度: O(T × N × W × F × M)
    状态空间大小: 约 1920 个状态

四、详细计算过程示例
--------------------------------------------------

4.1 初始资源购买计算
    第2关购买方案: 水120箱, 食物80箱
    水费用: 120 × 5 = 600元
    食物费用: 80 × 10 = 800元
    总费用: 600 + 800 = 1400元
    剩余资金: 10,000 - 1400 = 8,600元
    总重量: 120 × 3 + 80 × 2 = 520kg
    负重检查: 520 ≤ 1200 → ✅通过

4.2 每日消耗计算
    第2关以第1天移动为例:
    基础消耗: 水5箱/天, 食物7箱/天
    移动倍数: 2.0
    实际消耗: 水5 × 2.0 = 10.0箱
              食物7 × 2.0 = 14.0箱

4.3 挖矿收益计算
    第2关基础收益: 1,000元/天
    挖矿消耗: 水5 × 3.0 = 15.0箱
              食物7 × 3.0 = 21.0箱
    净收益: 1,000元 - 消耗成本

4.4 最终利润计算示例
    第2关最终状态: 现金20000元, 剩余水8箱, 剩余食物5箱
    水退回价值: 8 × 5 × 0.5 = 20.0元
    食物退回价值: 5 × 10 × 0.5 = 25.0元
    总退回价值: 20.0 + 25.0 = 45.0元
    总资产: 20000 + 45.0 = 20045.0元
    净利润: 20045.0 - 10,000 = 10,045.0元
    投资回报率: 100.4%

五、求解结果分析
--------------------------------------------------

5.1 最优解
    策略类型: mining
    最终资金: 20,085元
    净利润: 10,085元
    总用时: 22天
    投资回报率: 100.8%

5.2 路径分析
    最优路径: 1 → 2 → 3 → 12 → 21 → 30 → 38 → 46 → 55 → 64
    路径长度: 9步

5.3 资源统计
    总收入: 13,390元
    总水消耗: 228箱
    总食物消耗: 216箱
    平均日消耗: 水10.4箱/天, 食物9.8箱/天

六、结论与建议
--------------------------------------------------

6.1 模型特点
    • 本模型采用了混合整数规划方法
    • 充分考虑了时间、资源、负重等多重约束
    • 能够处理天气不确定性和多人博弈情况

6.2 算法优势
    • 保证找到全局最优解（天气已知情况）
    • 计算效率高，适合实时决策
    • 具有良好的可扩展性

6.3 应用价值
    • 为资源受限环境下的路径规划提供理论支持
    • 可应用于物流优化、应急救援等实际场景
    • 具有重要的教学和研究价值

================================================================================
                                     报告生成完毕                                     
                           生成时间: 2025-08-02 12:38:45                            
================================================================================