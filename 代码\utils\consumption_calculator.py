#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
消耗计算器
计算不同天气和行动下的资源消耗量
"""

from typing import Dict, List
try:
    from config.game_config import LevelConfig
except ImportError as e:
    print(f"导入配置模块失败: {e}")
    raise

class ConsumptionCalculator:
    """消耗计算器"""
    
    def __init__(self, config: LevelConfig):
        self.config = config
        # 为了向后兼容，添加mine_node属性
        if hasattr(config, 'mine_nodes') and config.mine_nodes:
            self.config.mine_node = config.mine_nodes[0]
        elif not hasattr(config, 'mine_node'):
            self.config.mine_node = None
        
    def get_daily_consumption(self, weather: str, action: str) -> Dict[str, int]:
        """
        计算每日消耗量

        Args:
            weather: 天气状况 ('晴朗', '高温', '沙暴')
            action: 行动类型 ('move', 'stay', 'mining')

        Returns:
            消耗量字典 {'water': 水量, 'food': 食物量}
        """
        base_consumption = self.config.consumption.get(weather, {})

        if not base_consumption:
            # 默认消耗 (晴朗天气)
            base_consumption = {'water': 3, 'food': 4}

        water_consumption = base_consumption.get('water', 3)
        food_consumption = base_consumption.get('food', 4)

        # 根据行动类型调整消耗
        if action == 'move':
            # 移动时消耗增加 (根据解决方案中的move_factor)
            water_consumption = int(water_consumption * self.config.move_factor)
            food_consumption = int(food_consumption * self.config.move_factor)
        elif action == 'mining':
            # 挖矿时消耗为基础消耗的3倍
            water_consumption = int(water_consumption * 3.0)
            food_consumption = int(food_consumption * 3.0)
        elif action == 'stay':
            # 停留时使用基础消耗
            pass

        # 多人关卡需要乘以人数
        if hasattr(self.config, 'players') and self.config.players > 1:
            water_consumption = int(water_consumption * self.config.players)
            food_consumption = int(food_consumption * self.config.players)

        return {
            'water': water_consumption,
            'food': food_consumption
        }
    
    def get_mining_income(self, weather: str) -> int:
        """
        计算挖矿收入
        
        Args:
            weather: 天气状况
            
        Returns:
            当日挖矿收入
        """
        # 根据解决方案，挖矿收入 = 基础收益 + 当天基础水消耗*5 + 当天基础食物消耗*10
        base_consumption = self.config.consumption.get(weather, {'water': 3, 'food': 4})
        
        daily_income = (self.config.base_income + 
                       base_consumption['water'] * self.config.water_price + 
                       base_consumption['food'] * self.config.food_price)
        
        return daily_income
    
    def calculate_total_consumption(self, path: List[int], mining_days: int, 
                                  weather_forecast: List[str]) -> Dict[str, int]:
        """
        计算整个行程的总消耗
        
        Args:
            path: 行程路径
            mining_days: 挖矿天数
            weather_forecast: 天气预报
            
        Returns:
            总消耗量字典
        """
        total_water = 0
        total_food = 0
        current_day = 0
        
        # 计算移动到矿山的消耗
        mine_node = self.config.mine_nodes[0] if self.config.mine_nodes else None
        if mine_node is None or mine_node not in path:
            return {'water': 0, 'food': 0}

        mine_index = path.index(mine_node)
        for i in range(mine_index):
            if current_day < len(weather_forecast):
                weather = weather_forecast[current_day]
                consumption = self.get_daily_consumption(weather, 'move')
                total_water += consumption['water']
                total_food += consumption['food']
            current_day += 1
        
        # 计算在矿山的消耗
        for day in range(mining_days):
            if current_day < len(weather_forecast):
                weather = weather_forecast[current_day]
                consumption = self.get_daily_consumption(weather, 'mining')
                total_water += consumption['water']
                total_food += consumption['food']
            current_day += 1
        
        # 计算从矿山到终点的消耗
        for i in range(mine_index + 1, len(path)):
            if current_day < len(weather_forecast):
                weather = weather_forecast[current_day]
                consumption = self.get_daily_consumption(weather, 'move')
                total_water += consumption['water']
                total_food += consumption['food']
            current_day += 1
        
        return {
            'water': total_water,
            'food': total_food,
            'total_days': current_day
        }
    
    def calculate_total_income(self, mining_days: int, start_day: int, 
                             weather_forecast: List[str]) -> int:
        """
        计算总挖矿收入
        
        Args:
            mining_days: 挖矿天数
            start_day: 开始挖矿的天数 (从0开始)
            weather_forecast: 天气预报
            
        Returns:
            总收入
        """
        total_income = 0
        
        for day in range(mining_days):
            current_day = start_day + day
            if current_day < len(weather_forecast):
                weather = weather_forecast[current_day]
                daily_income = self.get_mining_income(weather)
                total_income += daily_income
            else:
                # 超出预报范围，使用默认天气
                daily_income = self.get_mining_income('晴朗')
                total_income += daily_income
        
        return total_income
    
    def check_weight_constraint(self, water_needed: int, food_needed: int) -> bool:
        """
        检查负重约束
        
        Args:
            water_needed: 所需水量 (箱)
            food_needed: 所需食物量 (箱)
            
        Returns:
            是否满足负重约束
        """
        total_weight = (water_needed * self.config.water_weight + 
                       food_needed * self.config.food_weight)
        return total_weight <= self.config.max_weight
    
    def calculate_purchase_cost(self, water_needed: int, food_needed: int) -> int:
        """
        计算购买成本
        
        Args:
            water_needed: 所需水量 (箱)
            food_needed: 所需食物量 (箱)
            
        Returns:
            购买成本
        """
        return (water_needed * self.config.water_price + 
                food_needed * self.config.food_price)
    
    def get_consumption_summary(self, weather_forecast: List[str]) -> Dict:
        """
        获取消耗汇总信息
        
        Args:
            weather_forecast: 天气预报
            
        Returns:
            消耗汇总字典
        """
        weather_count = {}
        total_move_consumption = {'water': 0, 'food': 0}
        total_stay_consumption = {'water': 0, 'food': 0}
        
        for weather in weather_forecast:
            weather_count[weather] = weather_count.get(weather, 0) + 1
            
            move_consumption = self.get_daily_consumption(weather, 'move')
            stay_consumption = self.get_daily_consumption(weather, 'stay')
            
            total_move_consumption['water'] += move_consumption['water']
            total_move_consumption['food'] += move_consumption['food']
            total_stay_consumption['water'] += stay_consumption['water']
            total_stay_consumption['food'] += stay_consumption['food']
        
        return {
            'weather_count': weather_count,
            'total_move_consumption': total_move_consumption,
            'total_stay_consumption': total_stay_consumption,
            'total_days': len(weather_forecast)
        }
