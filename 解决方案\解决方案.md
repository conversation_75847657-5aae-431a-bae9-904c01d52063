
### **第一步：问题解构与数据结构化**

这是所有工作的基础。我们需要将题目中零散的信息转化为程序可以处理的、结构化的数据。

1. **定义核心参数 (Constants):**
   在代码的最开始，将所有固定不变的数值定义为常量。这让代码更易读、易修改。

   * `INITIAL_MONEY = 10000` (元)
   * `MAX_WEIGHT = 1200` (千克)
   * `DEADLINE_DAYS = 30` (天)
   * `K_MOVE_FACTOR = 2` (行走消耗系数，这是根据PDF推断的关键假设)
2. **结构化资源信息 (Data Structures):**
   使用字典 (Dictionary) 来存储水和食物的属性，便于按名称调用。

   ```python
   RESOURCES = {
       'water': {'price': 5, 'weight': 3},
       'food':  {'price': 10, 'weight': 2}
   }
   ```
3. **结构化消耗规则 (Data Structures):**
   使用嵌套字典来清晰地表示不同天气下的基础消耗量。

   ```python
   BASE_CONSUMPTION = {
       '晴朗': {'water': 5, 'food': 7},
       '高温': {'water': 8, 'food': 6},
       '沙暴': {'water': 10, 'food': 10}
   }
   ```
4. **结构化天气和地图数据 (Data Structures):**

   * **天气预报：** 使用一个列表 (List) 存储30天的天气情况。列表的索引 `i` 可以直接对应第 `i+1` 天。
     ```python
     WEATHER_FORECAST = ['高温', '高温', '晴朗', '沙暴', ...]
     ```
   * **地图路径：** 这是一个关键点！根据 `附件.docx`，路径是**固定**的。我们也用一个列表来表示这个唯一的路径。这大大简化了问题，我们不需要做寻路算法。
     ```python
     MAP_PATH = [1, 2, 6, 3, 4, 5, 8, ... , 27] # 将附件中的路径节点按顺序填入
     # 同时定义关键节点
     START_NODE = 1
     MINE_NODE = 12
     END_NODE = 27
     ```

### **第二步：核心数学模型与关键假设**

现在，我们用数学语言来描述这个问题。

1. **目标函数 (Objective Function):**
   我们的目标是最大化最终剩余资金 `Final_Money`。
   `Maximize: Final_Money = INITIAL_MONEY - Purchase_Cost + Mining_Income`
2. **决策变量 (Decision Variable):**
   问题的核心在于决定在矿山停留挖矿的天数。我们定义这个变量为 `d_mine`。
3. **约束条件 (Constraints):**

   * **时间约束：** 总耗时 `Total_Days <= 30`。
   * **负重约束：** 初始购买物资总重量 `Total_Weight <= 1200`。
     `Total_Weight = (Total_Water_Needed * 3) + (Total_Food_Needed * 2)`
4. **关键建模假设 (Crucial Assumptions):**

   * **假设1 (沙暴日行动规则):** 这是最重要的假设，源于对PDF文件的解读。**为了使问题有解，我们假设沙暴日依然可以移动，行程天数不被耽搁，但资源消耗按沙暴日的标准计算。** 如果死守“沙暴日必须停留”的字面规则，任务将无法在30天内完成。
   * **假设2 (最优购买原则):** 为了使最终资金最大化，我们在起点购买的物资量应该**精确等于**整个行程的总消耗量。任何超额购买的物资在终点只能半价出售，必然导致亏损。
   * **假设3 (行走消耗系数):** 行走消耗是基础消耗的 `k` 倍。根据PDF的参考，我们设定 `k=2`。

### **第三步：算法设计 - 策略迭代与评估**

我们的算法思路是，既然决策变量 `d_mine` 的取值范围很小（比如0到10天，可以先设一个合理的上限），我们可以遍历所有可能的策略，评估每种策略的结果，然后选出最优的那个。

**算法流程图:**

```
[开始] -> [For d_mine in [0, 1, 2, ... , 10]]:
             |
             V
[1. 模拟行程，计算总消耗] -> [2. 检查约束条件] -> [3. 计算最终资金]
             |                   | (不可行)           |
             |                   V                   V
             |<----------------[放弃该策略]       [记录策略结果]
             |
             V
[循环结束] -> [4. 比较所有可行策略的结果] -> [输出最优解] -> [结束]
```

**详细步骤分解:**

1. **主循环:**
   `for d_mine in range(11):`  (迭代0到10天挖矿策略)
2. **模拟行程与计算总消耗 (在循环内部):**
   这是算法的核心。对于每一个 `d_mine` 值，你需要模拟一遍完整的行程。

   * 初始化 `total_water = 0`, `total_food = 0`, `total_income = 0`, `days_spent = 0`。
   * **阶段一：起点 -\> 矿山:**
     * 从 `START_NODE` 开始，沿着 `MAP_PATH` 移动，直到 `MINE_NODE`。
     * 每移动一天，`days_spent` 加1。
     * 根据当天的天气 `WEATHER_FORECAST[days_spent]` 和行动（'move'），计算当日消耗，累加到 `total_water` 和 `total_food`。
   * **阶段二：在矿山挖矿:**
     * 停留 `d_mine` 天。
     * 对于这 `d_mine` 天中的每一天，`days_spent` 加1。
     * 根据当天的天气和行动（'stay'），计算当日消耗并累加。
     * 计算当日挖矿收入（`当天基础水消耗*5 + 当天基础食物消耗*10`），累加到 `total_income`。
   * **阶段三：矿山 -\> 终点:**
     * 从 `MINE_NODE` 继续沿着 `MAP_PATH` 移动，直到 `END_NODE`。
     * 与阶段一类似，累加每日的消耗和天数。
3. **检查约束并计算收益 (在循环内部):**

   * **时间检查:** `if days_spent > 30:`，则该策略不可行，`continue` 到下一个 `d_mine`。
   * **负重检查:**
     * `total_weight = total_water * 3 + total_food * 2`
     * `if total_weight > 1200:`，则该策略不可行，`continue`。
   * **计算收益:** 如果策略可行：
     * `purchase_cost = total_water * 5 + total_food * 10`
     * `final_money = INITIAL_MONEY - purchase_cost + total_income`
     * 将 `{ 'mining_days': d_mine, 'final_money': final_money, ... }` 存入一个结果列表。
4. **寻找最优解 (在循环外部):**

   * 遍历结果列表，找到 `final_money` 最大的那个字典。
   * 将该字典中存储的所有信息（最优挖矿天数、所需水量/食物、最终资金等）格式化输出。

### **第四步：Python代码实现蓝图**

根据上述算法，你可以设计以下几个核心函数来实现模块化编程。

* `function get_daily_consumption(day_index, action)`

  * **输入:** 天数索引（0-29），行动（'move' 或 'stay'）。
  * **功能:** 根据天气和行动，返回当天应消耗的水和食物量（一个字典）。
  * **逻辑:** 内部处理 `K_MOVE_FACTOR` 和沙暴日规则。
* `function evaluate_strategy(mining_days)`

  * **输入:** 挖矿天数 `d_mine`。
  * **功能:** 执行 **第三步** 中的第2点和第3点，即完整的行程模拟和评估。
  * **输出:** 返回一个包含所有计算结果的字典，或者在策略不可行时返回 `None`。
* `function main()`

  * **功能:** 程序主入口。
  * **逻辑:**
    1. 初始化一个空列表 `results`。
    2. 调用 `for d_mine in range(11):` 循环。
    3. 在循环中调用 `evaluate_strategy(d_mine)`。
    4. 将有效结果存入 `results` 列表。
    5. 循环结束后，分析 `results` 列表找到最优解。
    6. 打印最终结果。
    7. (可选) 调用一个新函数 `generate_result_csv(optimal_strategy)` 来生成最终的提交文件。
