#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
游戏配置文件
包含所有6个关卡的参数配置、地图数据、天气数据等
"""

from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass

@dataclass
class LevelConfig:
    """关卡配置数据类"""
    level: int
    players: int
    max_weight: int
    initial_money: int
    deadline_days: int
    base_income: int
    
    # 资源配置
    water_price: int
    water_weight: int
    food_price: int
    food_weight: int
    
    # 消耗配置 (箱数)
    consumption: Dict[str, Dict[str, int]]
    
    # 地图配置
    map_nodes: List[int]
    start_node: int
    mine_nodes: List[int]  # 支持多个矿山
    end_node: int
    village_nodes: List[int]
    node_connections: Dict[int, List[int]]
    
    # 天气配置
    weather_forecast: List[str]
    weather_known: bool  # 是否完全已知天气
    
    # 特殊规则
    move_factor: float  # 行走消耗系数

class GameConfig:
    """游戏配置管理类"""
    
    def __init__(self):
        self.levels = self._initialize_levels()
    
    def _initialize_levels(self) -> Dict[int, LevelConfig]:
        """初始化所有关卡配置"""
        levels = {}
        
        # 第一关配置
        levels[1] = LevelConfig(
            level=1,
            players=1,
            max_weight=1200,
            initial_money=10000,
            deadline_days=30,
            base_income=1000,
            water_price=5,
            water_weight=3,
            food_price=10,
            food_weight=2,
            consumption={
                '晴朗': {'water': 5, 'food': 7},
                '高温': {'water': 8, 'food': 6},
                '沙暴': {'water': 10, 'food': 10}
            },
            map_nodes=list(range(1, 28)),  # 1-27节点
            start_node=1,
            mine_nodes=[12],
            end_node=27,
            village_nodes=[15],
            node_connections=self._get_level1_connections(),
            weather_forecast=self._get_level1_weather(),
            weather_known=True,
            move_factor=2.0
        )
        
        # 第二关配置
        levels[2] = LevelConfig(
            level=2,
            players=1,
            max_weight=1200,
            initial_money=10000,
            deadline_days=30,
            base_income=1000,
            water_price=5,
            water_weight=3,
            food_price=10,
            food_weight=2,
            consumption={
                '晴朗': {'water': 5, 'food': 7},
                '高温': {'water': 8, 'food': 6},
                '沙暴': {'water': 10, 'food': 10}
            },
            map_nodes=list(range(1, 65)),  # 1-64节点
            start_node=1,
            mine_nodes=[30, 55],  # 两个矿山
            end_node=64,
            village_nodes=[39, 62],  # 两个村庄
            node_connections=self._get_level2_connections(),
            weather_forecast=self._get_level2_weather(),
            weather_known=True,
            move_factor=2.0
        )
        
        # 第三关配置
        levels[3] = LevelConfig(
            level=3,
            players=1,
            max_weight=1200,
            initial_money=10000,
            deadline_days=10,  # 10天截止
            base_income=200,   # 基础收益200元
            water_price=5,
            water_weight=3,
            food_price=10,
            food_weight=2,
            consumption={
                '晴朗': {'water': 3, 'food': 4},
                '高温': {'water': 9, 'food': 9},
                '沙暴': {'water': 10, 'food': 10}
            },
            map_nodes=list(range(1, 14)),  # 1-13节点
            start_node=1,
            mine_nodes=[9],
            end_node=13,
            village_nodes=[],  # 图中未标明村庄
            node_connections=self._get_level3_connections(),
            weather_forecast=self._get_level3_weather(),
            weather_known=False,  # 玩家不知道未来天气
            move_factor=2.0
        )
        
        # 第四关配置 (5x5地图)
        levels[4] = LevelConfig(
            level=4,
            players=1,
            max_weight=1200,
            initial_money=10000,
            deadline_days=30,
            base_income=1000,
            water_price=5,
            water_weight=3,
            food_price=10,
            food_weight=2,
            consumption={
                '晴朗': {'water': 3, 'food': 4},
                '高温': {'water': 9, 'food': 9},
                '沙暴': {'water': 10, 'food': 10}
            },
            map_nodes=list(range(1, 26)),  # 5x5 = 25个节点
            start_node=1,
            mine_nodes=[18],
            end_node=25,
            village_nodes=[14],
            node_connections=self._get_5x5_grid_connections(),
            weather_forecast=self._get_level4_weather(),
            weather_known=False,  # 玩家仅知道当天天气
            move_factor=2.0
        )
        
        # 第五关配置 (双人博弈)
        levels[5] = LevelConfig(
            level=5,
            players=2,
            max_weight=1200,
            initial_money=10000,
            deadline_days=10,
            base_income=200,
            water_price=5,
            water_weight=3,
            food_price=10,
            food_weight=2,
            consumption={
                '晴朗': {'water': 3, 'food': 4},
                '高温': {'water': 9, 'food': 9},
                '沙暴': {'water': 10, 'food': 10}
            },
            map_nodes=list(range(1, 14)),  # 13个节点
            start_node=1,
            mine_nodes=[9],
            end_node=13,
            village_nodes=[],
            node_connections=self._get_level5_connections(),
            weather_forecast=self._get_level5_weather(),
            weather_known=True,
            move_factor=2.0
        )
        
        # 第六关配置 (三人博弈)
        levels[6] = LevelConfig(
            level=6,
            players=3,
            max_weight=1200,
            initial_money=10000,
            deadline_days=30,
            base_income=1000,
            water_price=5,
            water_weight=3,
            food_price=10,
            food_weight=2,
            consumption={
                '晴朗': {'water': 3, 'food': 4},
                '高温': {'water': 9, 'food': 9},
                '沙暴': {'water': 10, 'food': 10}
            },
            map_nodes=list(range(1, 26)),  # 5x5地图
            start_node=1,
            mine_nodes=[18],
            end_node=25,
            village_nodes=[14],
            node_connections=self._get_5x5_grid_connections(),
            weather_forecast=self._get_level6_weather(),  # 30天天气，较少沙暴
            weather_known=False,  # 只知道当天天气
            move_factor=2.0
        )
        
        return levels
    
    def get_level_config(self, level: int) -> Optional[LevelConfig]:
        """获取指定关卡的配置"""
        return self.levels.get(level)
    
    def _get_level1_weather(self) -> List[str]:
        """第一关天气预报"""
        return ['高温', '高温', '晴朗', '沙暴', '晴朗', '高温', '沙暴', '晴朗',
                '高温', '高温', '沙暴', '高温', '晴朗', '高温', '高温',
                '高温', '沙暴', '沙暴', '高温', '高温', '晴朗', '晴朗',
                '高温', '晴朗', '沙暴', '高温', '晴朗', '晴朗', '高温', '高温']
    
    def _get_level2_weather(self) -> List[str]:
        """第二关天气预报"""
        return ['高温', '高温', '晴朗', '沙暴', '晴朗', '高温', '沙暴', '晴朗',
                '高温', '高温', '沙暴', '高温', '晴朗', '高温', '高温',
                '高温', '沙暴', '沙暴', '高温', '高温', '晴朗', '晴朗',
                '高温', '晴朗', '沙暴', '高温', '晴朗', '晴朗', '高温', '高温']
    
    def _get_level3_weather(self) -> List[str]:
        """第三关天气预报 (10天，无沙暴)"""
        return ['晴朗', '高温', '晴朗', '高温', '晴朗', '高温', '晴朗', '高温', '晴朗', '高温']
    
    def _get_level4_weather(self) -> List[str]:
        """第四关天气预报 (30天，较少沙暴)"""
        return ['晴朗', '晴朗', '高温', '晴朗', '高温', '晴朗', '晴朗', '高温',
                '晴朗', '高温', '晴朗', '沙暴', '晴朗', '高温', '晴朗',
                '晴朗', '高温', '晴朗', '高温', '晴朗', '晴朗', '高温',
                '晴朗', '沙暴', '晴朗', '高温', '晴朗', '晴朗', '高温', '晴朗']
    
    def _get_level5_weather(self) -> List[str]:
        """第五关天气预报 (10天)"""
        return ['晴朗', '高温', '晴朗', '晴朗', '晴朗', '晴朗', '高温', '高温', '高温', '高温']

    def _get_level6_weather(self) -> List[str]:
        """第六关天气预报 (30天，较少沙暴)"""
        # 30天中大约20天晴朗，8天高温，2天沙暴
        weather = ['晴朗'] * 20 + ['高温'] * 8 + ['沙暴'] * 2
        import random
        random.shuffle(weather)
        return weather

    def _get_level1_connections(self) -> Dict[int, List[int]]:
        """第一关地图连接关系 (27节点不规则地图)"""
        return {
            1: [2, 25], 2: [1, 3], 3: [2, 4], 4: [3, 5, 25], 5: [4, 6, 24],
            6: [5, 7, 22], 7: [6, 8], 8: [7, 9, 22], 9: [8, 10, 17], 10: [9, 11, 13],
            11: [10, 13], 12: [13, 14], 13: [10, 11, 12, 14, 15], 14: [12, 13, 16],
            15: [13, 16, 17], 16: [14, 15, 18], 17: [9, 15, 18, 20], 18: [16, 17, 19],
            19: [18, 20], 20: [17, 19, 21], 21: [20, 23, 26, 27], 22: [6, 8, 23],
            23: [21, 22, 24, 26], 24: [5, 23, 25], 25: [1, 4, 24, 26],
            26: [21, 23, 25, 27], 27: [21, 26]
        }

    def _get_level2_connections(self) -> Dict[int, List[int]]:
        """第二关地图连接关系 (8x8六边形网格)"""
        connections = {}

        # 8x8六边形网格的连接关系
        for i in range(1, 65):  # 节点1-64
            row = (i - 1) // 8  # 行号 0-7
            col = (i - 1) % 8   # 列号 0-7
            connections[i] = []

            # 左右邻居
            if col > 0:  # 左邻居
                connections[i].append(i - 1)
            if col < 7:  # 右邻居
                connections[i].append(i + 1)

            # 上下邻居（六边形网格特殊规律）
            if row > 0:  # 上方邻居
                connections[i].append(i - 8)
                # 六边形网格的斜向连接
                if col > 0:
                    connections[i].append(i - 9)  # 左上
                if col < 7:
                    connections[i].append(i - 7)  # 右上

            if row < 7:  # 下方邻居
                connections[i].append(i + 8)
                # 六边形网格的斜向连接
                if col > 0:
                    connections[i].append(i + 7)  # 左下
                if col < 7:
                    connections[i].append(i + 9)  # 右下

        return connections

    def _get_level3_connections(self) -> Dict[int, List[int]]:
        """第三关地图连接关系 (不规则13节点地图)"""
        return {
            1: [2, 4, 5],           # 起点
            2: [1, 3, 4],
            3: [2, 4, 8, 9],
            4: [1, 2, 3, 5, 7],
            5: [1, 4, 6, 7],
            6: [5, 7, 12],
            7: [4, 5, 6, 11, 12],
            8: [3, 9],
            9: [3, 8, 10, 11],      # 矿山
            10: [9, 11],
            11: [7, 9, 10, 12, 13],
            12: [6, 7, 11, 13],
            13: [11, 12]            # 终点
        }

    def _get_5x5_grid_connections(self) -> Dict[int, List[int]]:
        """5x5拼图状地图连接关系"""
        connections = {}
        for i in range(1, 26):  # 节点1-25
            row = (i - 1) // 5
            col = (i - 1) % 5
            connections[i] = []

            # 上下左右连接
            if row > 0:  # 上
                connections[i].append(i - 5)
            if row < 4:  # 下
                connections[i].append(i + 5)
            if col > 0:  # 左
                connections[i].append(i - 1)
            if col < 4:  # 右
                connections[i].append(i + 1)

        return connections

    def _get_level5_connections(self) -> Dict[int, List[int]]:
        """第五关地图连接关系 (不规则13节点)"""
        return {
            1: [2, 4, 5],  # 起点
            2: [1, 3, 4],
            3: [2, 4, 8, 9],
            4: [1, 2, 3, 5, 7],
            5: [1, 4, 6, 7],
            6: [5, 7, 12, 13],
            7: [4, 5, 6, 11, 12],
            8: [3, 9, 10],
            9: [3, 8, 10, 11],  # 矿山
            10: [8, 9, 11],
            11: [7, 9, 10, 12],
            12: [6, 7, 11, 13],
            13: [6, 12]  # 终点
        }
