#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整详细的数学模型展示器
包含所有约束条件、状态转移方程、边界条件等完整公式
"""

from typing import Dict, List, Optional
import json
from pathlib import Path

class CompleteMathematicalModel:
    """完整数学模型展示器"""
    
    def __init__(self, config, level: int):
        self.config = config
        self.level = level
        self._setup_config_adapter()
    
    def _setup_config_adapter(self):
        """设置配置适配器"""
        if hasattr(self.config, 'consumption'):
            sunny_consumption = self.config.consumption.get('晴朗', {'water': 3, 'food': 4})
            self.config.base_water_consumption = sunny_consumption['water']
            self.config.base_food_consumption = sunny_consumption['food']
        else:
            self.config.base_water_consumption = 3
            self.config.base_food_consumption = 4
        
        if hasattr(self.config, 'move_factor'):
            self.config.move_consumption_multiplier = self.config.move_factor
        else:
            self.config.move_consumption_multiplier = 2.0
        
        self.config.mine_consumption_multiplier = 1.5
        
        if hasattr(self.config, 'base_income'):
            self.config.mine_income = self.config.base_income
        else:
            self.config.mine_income = 1000
    
    def generate_complete_model(self) -> Dict:
        """生成完整的数学模型"""
        
        # 决策变量（更详细）
        decision_variables = {
            "二元决策变量": {
                "x_{i,j}^t": "第t天从节点i移动到节点j的决策变量 ∈ {0,1}",
                "s_i^t": "第t天在节点i停留的决策变量 ∈ {0,1}",
                "m_i^t": "第t天在矿山i挖矿的决策变量 ∈ {0,1}",
                "b_i^t": "第t天在村庄i购买资源的决策变量 ∈ {0,1}"
            },
            "连续变量": {
                "w_t": "第t天结束时的水量 ∈ ℝ⁺",
                "f_t": "第t天结束时的食物量 ∈ ℝ⁺", 
                "M_t": "第t天结束时的资金 ∈ ℝ⁺",
                "w_0": "初始购买的水量 ∈ ℝ⁺",
                "f_0": "初始购买的食物量 ∈ ℝ⁺",
                "w_b^t": "第t天在村庄购买的水量 ∈ ℝ⁺",
                "f_b^t": "第t天在村庄购买的食物量 ∈ ℝ⁺"
            }
        }
        
        # 参数定义（更完整）
        parameters = {
            "时间参数": {
                "T": f"时间上限 = {self.config.deadline_days}天",
                "t": "时间索引 ∈ {0,1,2,...,T}"
            },
            "空间参数": {
                "N": f"节点总数 = {len(self.config.map_nodes)}",
                "i,j": "节点索引 ∈ {1,2,...,N}",
                "S": f"起点 = {self.config.start_node}",
                "E": f"终点 = {self.config.end_node}",
                "M": f"矿山节点集合 = {self.config.mine_nodes}",
                "V": f"村庄节点集合 = {self.config.village_nodes}"
            },
            "资源参数": {
                "W_max": f"负重上限 = {self.config.max_weight}kg",
                "M_0": f"初始资金 = {self.config.initial_money}元",
                "P_w": f"水基准价格 = {self.config.water_price}元/箱",
                "P_f": f"食物基准价格 = {self.config.food_price}元/箱",
                "P_w^v": f"村庄水价格 = {self.config.water_price * 2}元/箱",
                "P_f^v": f"村庄食物价格 = {self.config.food_price * 2}元/箱"
            },
            "消耗参数": {
                "C_w^{base}": f"基础水消耗 = {self.config.base_water_consumption}箱/天",
                "C_f^{base}": f"基础食物消耗 = {self.config.base_food_consumption}箱/天",
                "α": f"移动消耗倍数 = {self.config.move_consumption_multiplier}",
                "β": f"挖矿消耗倍数 = {self.config.mine_consumption_multiplier}",
                "γ": "天气影响因子 ∈ [1.0, 1.5]"
            },
            "收益参数": {
                "R": f"挖矿基础收益 = {self.config.mine_income}元/天",
                "R_shared": f"多人挖矿收益 = R/{self.config.players}元/天"
            }
        }
        
        # 完整约束条件
        constraints = self._generate_complete_constraints()
        
        # 目标函数（更详细）
        objective_functions = {
            "主目标函数": "max Z = M_T + 0.5 × P_w × w_T + 0.5 × P_f × f_T",
            "展开形式": "max Z = M_T + 2.5 × w_T + 5.0 × f_T",
            "经济意义": "最大化最终总资产（现金 + 剩余资源退回价值）"
        }
        
        # 状态转移方程
        state_transitions = self._generate_state_transitions()
        
        # 边界条件
        boundary_conditions = self._generate_boundary_conditions()
        
        # 根据配置文件中的weather_known属性确定问题性质
        if hasattr(self.config, 'weather_known') and self.config.weather_known:
            problem_nature = "确定性动态优化问题（天气完全已知）"
            weather_info = f"天气信息：完全已知，提供{len(self.config.weather_forecast) if hasattr(self.config, 'weather_forecast') else 0}天完整预报"
        else:
            problem_nature = "随机性动态优化问题（天气仅知当天）"
            weather_info = "天气信息：仅知当天，存在不确定性，需要动态决策"

        return {
            "模型类型": "混合整数非线性规划 (MINLP)",
            "问题性质": problem_nature,
            "天气信息": weather_info,
            "决策变量": decision_variables,
            "参数定义": parameters,
            "约束条件": constraints,
            "目标函数": objective_functions,
            "状态转移方程": state_transitions,
            "边界条件": boundary_conditions,
            "复杂度分析": {
                "变量数量": f"二元变量: O(T×N²), 连续变量: O(T×N)",
                "约束数量": f"O(T×N²) 个约束",
                "时间复杂度": "O(T×N×W×F×M) [动态规划]",
                "空间复杂度": "O(T×N×W×F×M)",
                "NP难度": "NP-Hard (含整数变量的非线性规划)"
            }
        }
    
    def _generate_complete_constraints(self) -> Dict:
        """生成完整的约束条件"""
        return {
            "1. 初始约束": [
                "M_0 = 10000  (初始资金)",
                "P_w × w_0 + P_f × f_0 ≤ M_0  (初始购买约束)",
                "w_0 + f_0 ≤ W_max  (初始负重约束)",
                "w_0, f_0 ≥ 0  (非负约束)"
            ],
            
            "2. 路径约束": [
                "∑_{j∈N(i)} x_{i,j}^t + s_i^t + m_i^t = 1, ∀i,t  (每天只能选择一个行动)",
                "∑_{i∈N} ∑_{j∈N(i)} x_{i,j}^t ≤ 1, ∀t  (每天最多移动一次)",
                "x_{i,j}^t = 0 if (i,j) ∉ E  (只能在相邻节点间移动)",
                "∑_{j∈N(i)} x_{j,i}^{t-1} = ∑_{j∈N(i)} x_{i,j}^t + s_i^t + m_i^t, ∀i,t≥1  (路径连续性)"
            ],
            
            "3. 时间约束": [
                "∑_{t=1}^T ∑_{j∈N(E)} x_{j,E}^t ≥ 1  (必须到达终点)",
                "∑_{t=1}^T t × ∑_{j∈N(E)} x_{j,E}^t ≤ T  (时间限制)",
                "x_{i,j}^t = 0 if weather_t = '沙暴', ∀i,j  (沙暴日禁止移动)"
            ],
            
            "4. 资源平衡约束": [
                "w_{t+1} = w_t - C_w^t + w_b^t, ∀t  (水量平衡)",
                "f_{t+1} = f_t - C_f^t + f_b^t, ∀t  (食物平衡)",
                "w_t + f_t ≤ W_max, ∀t  (负重约束)",
                "w_t, f_t ≥ 0, ∀t  (资源非负)"
            ],
            
            "5. 消耗计算约束": [
                "C_w^t = C_w^{base} × (1 + α×∑_{i,j} x_{i,j}^t + (β-1)×∑_i m_i^t) × γ_t",
                "C_f^t = C_f^{base} × (1 + α×∑_{i,j} x_{i,j}^t + (β-1)×∑_i m_i^t)",
                "γ_t = 1.0 if weather_t ∈ {'晴朗'}, 1.2 if weather_t = '高温', 1.5 if weather_t = '沙暴'"
            ],
            
            "6. 资金平衡约束": [
                "M_{t+1} = M_t + R×∑_i m_i^t - P_w^v×w_b^t - P_f^v×f_b^t, ∀t",
                "M_t ≥ P_w^v×w_b^t + P_f^v×f_b^t, ∀t  (购买资金约束)",
                "M_t ≥ 0, ∀t  (资金非负)"
            ],
            
            "7. 挖矿约束": [
                "m_i^t = 0 if i ≠ M, ∀t  (只能在矿山挖矿)",
                "m_M^t ≤ s_M^{t-1}, ∀t≥1  (到达矿山当天不能挖矿)",
                "∑_{t=1}^T m_M^t ≤ T-2  (挖矿天数限制)"
            ],
            
            "8. 村庄购买约束": [
                "b_i^t = 0 if i ∉ V, ∀t  (只能在村庄购买)",
                "w_b^t ≤ W_max × b_i^t, ∀i∈V,t  (购买量约束)",
                "f_b^t ≤ W_max × b_i^t, ∀i∈V,t",
                "w_b^t + f_b^t ≤ W_max - w_t - f_t, ∀t  (购买后负重约束)"
            ],
            
            "9. 多人博弈约束": [
                f"R_actual = R / max(1, players_mining), if players > 1",
                f"C_multiplier = 1 + 0.5 × (players_moving - 1), if players > 1",
                f"P_multiplier = 1 + 0.5 × (players_buying - 1), if players > 1"
            ] if self.config.players > 1 else ["单人模式，无博弈约束"],
            
            "10. 逻辑约束": [
                "x_{i,j}^t ∈ {0,1}, ∀i,j,t",
                "s_i^t ∈ {0,1}, ∀i,t", 
                "m_i^t ∈ {0,1}, ∀i,t",
                "b_i^t ∈ {0,1}, ∀i,t"
            ]
        }
    
    def _generate_state_transitions(self) -> Dict:
        """生成状态转移方程"""
        return {
            "动态规划状态定义": "V(t,i,w,f,m) = 从第t天在节点i，拥有w箱水、f箱食物、m元资金开始的最大期望收益",
            
            "状态转移方程": {
                "停留": "V(t,i,w,f,m) → V(t+1,i,w-C_w^stay,f-C_f^stay,m)",
                "移动": "V(t,i,w,f,m) → max_j V(t+1,j,w-C_w^move,f-C_f^move,m)",
                "挖矿": "V(t,i,w,f,m) → V(t+1,i,w-C_w^mine,f-C_f^mine,m+R)",
                "购买": "V(t,i,w,f,m) → V(t+1,i,w+w_b,f+f_b,m-cost)"
            },
            
            "贝尔曼方程": "V(t,i,w,f,m) = max{V_stay, V_move, V_mine, V_buy}",
            
            "终止条件": "V(T,E,w,f,m) = m + 0.5×P_w×w + 0.5×P_f×f",
            
            "不可行状态": "V(t,i,w,f,m) = -∞ if w<0 or f<0 or m<0"
        }
    
    def _generate_boundary_conditions(self) -> Dict:
        """生成边界条件"""
        return {
            "初始条件": [
                f"t=0: 位置={self.config.start_node}, 水量=w_0, 食物=f_0, 资金=M_0-P_w×w_0-P_f×f_0"
            ],
            "终止条件": [
                f"t≤T: 必须到达节点{self.config.end_node}",
                "资源耗尽即游戏失败: w_t=0 or f_t=0 → 不可行"
            ],
            "可行域边界": [
                "w_t + f_t ≤ W_max, ∀t",
                "w_t, f_t, M_t ≥ 0, ∀t",
                "t ∈ {0,1,2,...,T}"
            ]
        }
    
    def save_complete_model(self, filename: str) -> str:
        """保存完整数学模型到文件"""
        model = self.generate_complete_model()
        
        output_dir = Path("results")
        output_dir.mkdir(exist_ok=True)
        filepath = output_dir / filename
        
        with open(filepath, 'w', encoding='utf-8') as f:
            json.dump(model, f, ensure_ascii=False, indent=2)
        
        return str(filepath)
