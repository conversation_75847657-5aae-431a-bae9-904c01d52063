#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
生成策略比较报告
包含终端信息和策略分析
"""

from datetime import datetime
from config.game_config import GameConfig
from solvers.level_solver import CorrectedLevelSolver
from utils.strategy_comparison_analyzer import StrategyComparisonAnalyzer


def generate_complete_report():
    """生成完整的策略比较报告"""
    
    print("🔄 正在生成完整的策略比较报告...")
    
    # 创建报告内容
    report_lines = []
    
    # 添加报告头部
    report_lines.extend([
        "=" * 100,
        "穿越沙漠游戏 - 完整求解结果与策略分析报告".center(100),
        "=" * 100,
        f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
        f"报告内容: 终端输出信息 + 策略比较分析",
        "=" * 100,
        "",
        ""
    ])
    
    # 第一部分：终端输出信息
    report_lines.extend([
        "第一部分：详细求解过程".center(100),
        "=" * 100,
        ""
    ])
    
    config = GameConfig()
    total_profit = 0
    level_results = []
    
    # 运行所有关卡并收集结果
    for level in range(1, 7):
        level_config = config.get_level_config(level)
        if not level_config:
            continue
            
        print(f"正在求解第{level}关...")
        
        # 添加关卡分隔符
        report_lines.extend([
            "=" * 60,
            f"修正版求解器 - 第{level}关".center(60),
            "=" * 60,
            ""
        ])
        
        # 添加关卡规则信息
        weather_info = f"完全已知（{len(level_config.weather_forecast)}天预报）" if level_config.weather_known else "仅知当天天气"
        
        report_lines.extend([
            f"📋 第{level}关规则:",
            f"   玩家数量: {level_config.players}人",
            f"   负重上限: {level_config.max_weight}kg",
            f"   时间限制: {level_config.deadline_days}天",
            f"   初始资金: {level_config.initial_money:,}元",
            f"   天气信息: {weather_info}",
            f"   🚨 沙暴日必须停留（不能移动）",
            f"   ⛏️  矿山是可选的（不强制访问）",
            f"   🏪 村庄可补给资源（价格2倍）",
        ])
        
        if level_config.village_nodes:
            report_lines.append(f"   村庄位置: {level_config.village_nodes}")
        report_lines.append(f"   矿山位置: {level_config.mine_nodes}")
        
        # 求解关卡
        solver = CorrectedLevelSolver(level_config)
        result = solver.solve()
        
        if result and result.is_feasible:
            net_profit = result.final_money - level_config.initial_money
            total_profit += net_profit
            
            # 添加求解结果
            report_lines.extend([
                f"修正版求解器 - 第{level}关",
                f"{'天气完全已知，使用动态规划求解' if level_config.weather_known else '仅知当天天气，使用贪心策略'}",
                f"{'使用保守策略求解仅知当天天气的关卡' if not level_config.weather_known else ''}",
                "",
                f"✅ 求解成功！",
                f"   策略类型: {result.strategy_type}",
                f"   最终资金: {result.final_money:,}元",
                f"   净利润: {net_profit:+,}元",
                f"   总用时: {result.final_day}天",
                f"   路径: {' → '.join(map(str, result.path))}",
            ])
            
            # 添加收入信息
            total_income = sum(day.get('income', 0) for day in result.daily_log)
            if total_income > 0:
                report_lines.append(f"   总收入: {total_income:,}元")
            else:
                report_lines.append(f"   总收入: 0元")
            
            # 添加消耗信息
            total_water = sum(day.get('water_consumed', 0) for day in result.daily_log)
            total_food = sum(day.get('food_consumed', 0) for day in result.daily_log)
            report_lines.append(f"   总消耗: 水{total_water}箱, 食物{total_food}箱")
            
            # 添加沙暴日信息
            storm_days = sum(1 for day in result.daily_log if day.get('weather') == '沙暴')
            if storm_days > 0:
                report_lines.append(f"   沙暴日: {storm_days}天（均在原地停留）")
            
            report_lines.extend([
                f"每日日志已导出: results/level_{level}_daily_log.csv",
                f"   📁 结果已导出到: results",
                ""
            ])
            
            # 保存结果用于汇总
            level_results.append({
                'level': level,
                'strategy': result.strategy_type,
                'final_money': result.final_money,
                'net_profit': net_profit
            })
        else:
            report_lines.extend([
                f"❌ 求解失败",
                ""
            ])
    
    # 添加汇总信息
    report_lines.extend([
        "=" * 60,
        "所有关卡求解汇总".center(60),
        "=" * 60,
    ])
    
    for result in level_results:
        report_lines.append(f"第{result['level']}关: {result['strategy']:<7} 策略, 最终资金 {result['final_money']:,}元, 净利润 {result['net_profit']:+,}元")
    
    report_lines.extend([
        "",
        f"总净利润: {total_profit:,}元",
        f"平均净利润: {total_profit//6:,}元",
        "",
        ""
    ])
    
    # 第二部分：策略分析
    report_lines.extend([
        "第二部分：策略比较分析".center(100),
        "=" * 100,
        ""
    ])
    
    # 使用策略分析器生成分析内容
    analyzer = StrategyComparisonAnalyzer()
    strategy_analysis = analyzer.analyze_all_levels()
    
    # 将策略分析添加到报告中（跳过重复的头部）
    analysis_lines = strategy_analysis.split('\n')
    # 找到第一个关卡分析的开始位置
    start_idx = 0
    for i, line in enumerate(analysis_lines):
        if "第1关策略分析" in line:
            start_idx = max(0, i - 2)  # 包含分隔符
            break
    
    report_lines.extend(analysis_lines[start_idx:])
    
    # 保存完整报告
    timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
    filename = f"complete_strategy_report_{timestamp}.txt"
    filepath = f"results/{filename}"
    
    with open(filepath, 'w', encoding='utf-8') as f:
        f.write('\n'.join(report_lines))
    
    print(f"✅ 完整策略比较报告已生成: {filepath}")
    print(f"📊 报告包含:")
    print(f"   • 所有关卡的详细求解过程")
    print(f"   • 策略选择的原因分析")
    print(f"   • Mining vs Direct vs Village策略比较")
    print(f"   • 总体表现分析和优化建议")
    
    return filepath


if __name__ == "__main__":
    generate_complete_report()
