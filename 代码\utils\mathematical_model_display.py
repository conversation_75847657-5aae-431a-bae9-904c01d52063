#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
穿越沙漠数学模型详细展示器
展示完整的数学建模过程、约束条件、目标函数和求解步骤
"""

import math
from typing import Dict, List, Tuple, Optional
from dataclasses import dataclass
import json

@dataclass
class MathematicalModel:
    """数学模型结构"""
    variables: Dict[str, str]  # 决策变量
    parameters: Dict[str, str]  # 参数定义
    constraints: List[str]     # 约束条件
    objective: str             # 目标函数
    solution_method: str       # 求解方法

class MathematicalModelDisplay:
    """数学模型展示器"""
    
    def __init__(self, config):
        self.config = config
        self.level = config.level

        # 适配器：处理配置属性差异
        self._setup_config_adapter()

    def _setup_config_adapter(self):
        """设置配置适配器，处理属性名称差异"""
        # 基础消耗量（使用晴朗天气的消耗作为基础）
        if hasattr(self.config, 'consumption'):
            sunny_consumption = self.config.consumption.get('晴朗', {'water': 3, 'food': 4})
            self.config.base_water_consumption = sunny_consumption['water']
            self.config.base_food_consumption = sunny_consumption['food']
        else:
            self.config.base_water_consumption = 3
            self.config.base_food_consumption = 4

        # 消耗倍数
        if hasattr(self.config, 'move_factor'):
            self.config.move_consumption_multiplier = self.config.move_factor
        else:
            self.config.move_consumption_multiplier = 2.0

        self.config.mine_consumption_multiplier = 1.5  # 默认挖矿消耗倍数

        # 挖矿收益
        if hasattr(self.config, 'base_income'):
            self.config.mine_income = self.config.base_income
        else:
            self.config.mine_income = 1000

        # 图结构
        if hasattr(self.config, 'node_connections'):
            self.config.graph = self.config.node_connections
        else:
            self.config.graph = {}

    def display_complete_model(self):
        """展示完整的数学模型"""
        print("=" * 80)
        print(f"穿越沙漠问题第{self.level}关 - 完整数学模型")
        print("=" * 80)
        
        # 1. 问题描述
        self._display_problem_description()
        
        # 2. 数学模型建立
        model = self._build_mathematical_model()
        self._display_mathematical_model(model)
        
        # 3. 约束条件详解
        self._display_constraint_analysis()
        
        # 4. 求解方法
        self._display_solution_method()
        
        # 5. 计算示例
        self._display_calculation_example()
        
        return model
    
    def _display_problem_description(self):
        """展示问题描述"""
        print("\n📋 问题描述:")
        print("-" * 40)
        print(f"• 玩家数量: {self.config.players}人")
        print(f"• 初始资金: {self.config.initial_money}元")
        print(f"• 负重上限: {self.config.max_weight}kg")
        print(f"• 时间限制: {self.config.deadline_days}天")
        print(f"• 地图节点: {len(self.config.map_nodes)}个区域")
        
        if hasattr(self.config, 'mine_node'):
            print(f"• 矿山位置: 节点{self.config.mine_node}")
            print(f"• 挖矿收益: {self.config.mine_income}元/天")
        
        if self.config.village_nodes:
            print(f"• 村庄位置: {self.config.village_nodes}")
        
        print(f"• 水价格: {self.config.water_price}元/箱")
        print(f"• 食物价格: {self.config.food_price}元/箱")
    
    def _build_mathematical_model(self) -> MathematicalModel:
        """构建数学模型"""
        
        # 决策变量
        variables = {
            "x_ij^t": "第t天从节点i移动到节点j的决策变量 (0或1)",
            "s_i^t": "第t天在节点i停留的决策变量 (0或1)", 
            "m_i^t": "第t天在矿山i挖矿的决策变量 (0或1)",
            "w_0": "初始购买的水量 (箱)",
            "f_0": "初始购买的食物量 (箱)",
            "w_t": "第t天结束时的水量 (箱)",
            "f_t": "第t天结束时的食物量 (箱)",
            "M_t": "第t天结束时的资金 (元)"
        }
        
        # 参数定义
        parameters = {
            "T": f"时间限制 = {self.config.deadline_days}天",
            "W_max": f"负重上限 = {self.config.max_weight}kg",
            "M_0": f"初始资金 = {self.config.initial_money}元",
            "P_w": f"水价格 = {self.config.water_price}元/箱",
            "P_f": f"食物价格 = {self.config.food_price}元/箱",
            "C_w^base": f"基础水消耗 = {self.config.base_water_consumption}箱/天",
            "C_f^base": f"基础食物消耗 = {self.config.base_food_consumption}箱/天",
            "α": f"移动消耗倍数 = {self.config.move_consumption_multiplier}",
            "β": f"挖矿消耗倍数 = {self.config.mine_consumption_multiplier}",
            "R": f"挖矿收益 = {self.config.mine_income if hasattr(self.config, 'mine_income') else 0}元/天"
        }
        
        # 约束条件
        constraints = [
            "初始资金约束: P_w × w_0 + P_f × f_0 ≤ M_0",
            "负重约束: w_t + f_t ≤ W_max, ∀t ∈ [0,T]",
            "时间约束: ∑(x_ij^t + s_i^t) = 1, ∀t ∈ [1,T]",
            "路径连续性: ∑_j x_ji^t = ∑_j x_ij^(t+1) + s_i^(t+1), ∀i,t",
            "资源消耗约束: w_(t+1) = w_t - C_w(action_t, weather_t)",
            "非负约束: w_t ≥ 0, f_t ≥ 0, ∀t",
            "沙暴约束: 沙暴日必须停留, x_ij^t = 0 if weather_t = '沙暴'",
            "终点约束: 必须在截止日期前到达终点"
        ]
        
        # 目标函数
        objective = "max Z = M_T + 0.5 × P_w × w_T + 0.5 × P_f × f_T"
        
        # 求解方法
        if self.level in [1, 2, 5]:
            solution_method = "动态规划 + 策略枚举 (天气已知)"
        else:
            solution_method = "贪心算法 + 蒙特卡洛模拟 (天气未知)"
        
        return MathematicalModel(
            variables=variables,
            parameters=parameters, 
            constraints=constraints,
            objective=objective,
            solution_method=solution_method
        )
    
    def _display_mathematical_model(self, model: MathematicalModel):
        """展示数学模型"""
        print("\n🔢 数学模型:")
        print("-" * 40)
        
        print("\n📊 决策变量:")
        for var, desc in model.variables.items():
            print(f"  {var}: {desc}")
        
        print("\n📋 参数定义:")
        for param, desc in model.parameters.items():
            print(f"  {param}: {desc}")
        
        print("\n🎯 目标函数:")
        print(f"  {model.objective}")
        print("  说明: 最大化最终资金，包括剩余资源的退回价值")
        
        print("\n⚖️ 约束条件:")
        for i, constraint in enumerate(model.constraints, 1):
            print(f"  {i}. {constraint}")
    
    def _display_constraint_analysis(self):
        """展示约束条件详细分析"""
        print("\n🔍 约束条件详细分析:")
        print("-" * 40)
        
        print("\n1️⃣ 资源消耗模型:")
        print("   根据行动类型和天气条件，资源消耗计算如下:")
        print("   • 停留: C_w = C_w^base, C_f = C_f^base")
        print("   • 移动: C_w = α × C_w^base, C_f = α × C_f^base")
        print("   • 挖矿: C_w = β × C_w^base, C_f = β × C_f^base")
        
        print("\n2️⃣ 天气影响模型:")
        print("   • 晴朗: 正常消耗")
        print("   • 高温: 水消耗增加")
        print("   • 沙暴: 强制停留，不能移动")
        
        print("\n3️⃣ 多人博弈约束 (第5-6关):")
        if self.config.players > 1:
            print("   • 同时移动: 消耗倍数增加")
            print("   • 同时挖矿: 收益分摊")
            print("   • 同时购买: 价格上涨")
        else:
            print("   • 单人模式，无博弈约束")
    
    def _display_solution_method(self):
        """展示求解方法"""
        print("\n🧮 求解方法:")
        print("-" * 40)
        
        if self.level in [1, 2, 5]:
            print("📈 动态规划方法 (天气已知):")
            print("   1. 状态定义: State(day, location, water, food, money)")
            print("   2. 状态转移: 枚举所有可能的行动")
            print("   3. 最优子结构: 每个状态的最优解由子状态决定")
            print("   4. 策略枚举: 遍历不同的挖矿天数策略")
            print("   5. 剪枝优化: 排除不可行的状态")
        else:
            print("🎲 贪心算法 + 蒙特卡洛模拟 (天气未知):")
            print("   1. 贪心策略: 基于当前信息做最优决策")
            print("   2. 风险评估: 考虑未来天气的不确定性")
            print("   3. 多次模拟: 生成多种可能的天气序列")
            print("   4. 策略选择: 选择期望收益最高的策略")
    
    def _display_calculation_example(self):
        """展示计算示例"""
        print("\n💡 计算示例:")
        print("-" * 40)
        
        print("以第1天移动为例:")
        print(f"• 基础水消耗: {self.config.base_water_consumption}箱")
        print(f"• 基础食物消耗: {self.config.base_food_consumption}箱")
        print(f"• 移动倍数: {self.config.move_consumption_multiplier}")
        
        move_water = self.config.base_water_consumption * self.config.move_consumption_multiplier
        move_food = self.config.base_food_consumption * self.config.move_consumption_multiplier
        
        print(f"• 移动水消耗: {self.config.base_water_consumption} × {self.config.move_consumption_multiplier} = {move_water}箱")
        print(f"• 移动食物消耗: {self.config.base_food_consumption} × {self.config.move_consumption_multiplier} = {move_food}箱")
        
        if hasattr(self.config, 'mine_income'):
            print(f"\n挖矿收益计算:")
            print(f"• 基础收益: {self.config.mine_income}元/天")
            if self.config.players > 1:
                shared_income = self.config.mine_income / self.config.players
                print(f"• 多人分摊: {self.config.mine_income} ÷ {self.config.players} = {shared_income}元/天")
    
    def export_model_to_file(self, filename: str):
        """导出数学模型到文件"""
        model = self._build_mathematical_model()
        
        model_data = {
            "level": self.level,
            "problem_type": "穿越沙漠优化问题",
            "model_type": "混合整数规划 (MIP)",
            "variables": model.variables,
            "parameters": model.parameters,
            "constraints": model.constraints,
            "objective": model.objective,
            "solution_method": model.solution_method,
            "complexity_analysis": {
                "time_complexity": "O(T × N × W × F)" if self.level in [1,2,5] else "O(T × N)",
                "space_complexity": "O(T × N × W × F)" if self.level in [1,2,5] else "O(N)",
                "variables_count": f"约 {self.config.deadline_days * len(self.config.map_nodes)}个",
                "constraints_count": f"约 {len(model.constraints) * self.config.deadline_days}个"
            }
        }
        
        with open(filename, 'w', encoding='utf-8') as f:
            json.dump(model_data, f, ensure_ascii=False, indent=2)
        
        print(f"📁 数学模型已导出到: {filename}")

def demonstrate_mathematical_modeling():
    """演示数学建模过程"""
    print("🎓 穿越沙漠问题数学建模演示")
    print("=" * 60)
    
    # 这里可以加载配置并演示
    print("本演示展示了完整的数学建模过程:")
    print("1. 问题分析与建模")
    print("2. 决策变量定义") 
    print("3. 约束条件建立")
    print("4. 目标函数构建")
    print("5. 求解算法设计")
    print("6. 结果分析与验证")

class AlgorithmDetailDisplay:
    """算法详细展示器"""

    def __init__(self, config):
        self.config = config

    def display_dynamic_programming_algorithm(self):
        """展示动态规划算法详解"""
        print("\n🔄 动态规划算法详解:")
        print("=" * 50)

        print("📊 状态定义:")
        print("   State(t, i, w, f, m) 表示:")
        print("   • t: 当前天数")
        print("   • i: 当前位置节点")
        print("   • w: 当前水量")
        print("   • f: 当前食物量")
        print("   • m: 当前资金")

        print("\n🔄 状态转移方程:")
        print("   对于每个状态 (t, i, w, f, m)，可以转移到:")
        print("   1. 停留: (t+1, i, w-C_w^stay, f-C_f^stay, m+income)")
        print("   2. 移动到j: (t+1, j, w-C_w^move, f-C_f^move, m)")
        print("   3. 挖矿: (t+1, i, w-C_w^mine, f-C_f^mine, m+R)")

        print("\n🎯 最优子结构:")
        print("   V(t, i, w, f, m) = max{")
        print("     V(t+1, i, w-C_w^stay, f-C_f^stay, m+income),")
        print("     max_j V(t+1, j, w-C_w^move, f-C_f^move, m),")
        print("     V(t+1, i, w-C_w^mine, f-C_f^mine, m+R)")
        print("   }")

        print("\n⚡ 算法复杂度:")
        print(f"   时间复杂度: O(T × N × W × F × M)")
        print(f"   空间复杂度: O(T × N × W × F × M)")
        print(f"   其中: T={self.config.deadline_days}, N={len(self.config.map_nodes)}")

    def display_greedy_algorithm(self):
        """展示贪心算法详解"""
        print("\n🎯 贪心算法详解:")
        print("=" * 50)

        print("🧠 贪心策略:")
        print("   1. 优先选择收益最高的行动")
        print("   2. 考虑资源约束和时间限制")
        print("   3. 动态调整策略应对天气变化")

        print("\n📈 决策函数:")
        print("   Score(action) = Expected_Benefit / Resource_Cost")
        print("   其中:")
        print("   • Expected_Benefit: 期望收益")
        print("   • Resource_Cost: 资源消耗成本")

        print("\n🔄 算法流程:")
        print("   1. 初始化: 购买最优资源组合")
        print("   2. 每日决策: 根据当前状态选择最优行动")
        print("   3. 风险评估: 预留足够资源应对不确定性")
        print("   4. 终止条件: 到达终点或资源耗尽")

if __name__ == "__main__":
    demonstrate_mathematical_modeling()
