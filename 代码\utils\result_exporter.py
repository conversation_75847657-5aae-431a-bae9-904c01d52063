#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
结果导出器
导出求解结果到各种格式
"""

import json
import csv
from typing import Dict, List
from pathlib import Path
from datetime import datetime

class ResultExporter:
    """结果导出器"""
    
    def __init__(self):
        pass
    
    def export_daily_log(self, daily_log: List[Dict], file_path: Path):
        """导出每日详细日志"""
        try:
            with open(file_path, 'w', newline='', encoding='utf-8') as f:
                if daily_log:
                    # 使用英文字段名作为CSV字段名，避免编码问题
                    fieldnames = [
                        'day', 'location', 'weather', 'action',
                        'water_consumed', 'food_consumed', 'income'
                    ]

                    writer = csv.DictWriter(f, fieldnames=fieldnames)

                    # 写入表头
                    writer.writeheader()

                    # 写入数据
                    for log in daily_log:
                        # 只写入存在的字段
                        row = {k: log.get(k, '') for k in fieldnames}
                        writer.writerow(row)
                        
            print(f"每日日志已导出: {file_path}")
            
        except Exception as e:
            print(f"导出每日日志失败: {e}")
    
    def export_summary(self, result, file_path: Path):
        """导出汇总结果"""
        try:
            summary_data = {
                'level': result.level,
                'mining_days': result.mining_days,
                'total_days': result.total_days,
                'final_money': result.final_money,
                'water_needed': result.water_needed,
                'food_needed': result.food_needed,
                'total_income': result.total_income,
                'purchase_cost': result.purchase_cost,
                'net_profit': result.final_money - 10000,
                'path': result.path,
                'is_feasible': result.is_feasible,
                'export_time': datetime.now().isoformat()
            }
            
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(summary_data, f, ensure_ascii=False, indent=2)
                
            print(f"汇总结果已导出: {file_path}")
            
        except Exception as e:
            print(f"导出汇总结果失败: {e}")
    
    def export_all_levels_summary(self, results: Dict, file_path: Path):
        """导出所有关卡的汇总对比"""
        try:
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as f:
                fieldnames = [
                    '关卡', '玩家数', '挖矿天数', '总用时', '最终资金', '净利润',
                    '水量需求', '食物需求', '总收入', '购买成本', '可行性'
                ]
                
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                
                for level in sorted(results.keys()):
                    result = results[level]
                    # 从配置中获取玩家数
                    from config.game_config import GameConfig
                    config = GameConfig()
                    level_config = config.get_level_config(level)
                    players = level_config.players if level_config else 1

                    row = {
                        '关卡': level,
                        '玩家数': players,
                        '挖矿天数': result.mining_days,
                        '总用时': result.total_days,
                        '最终资金': result.final_money,
                        '净利润': result.final_money - 10000,
                        '水量需求': result.water_needed,
                        '食物需求': result.food_needed,
                        '总收入': result.total_income,
                        '购买成本': result.purchase_cost,
                        '可行性': '可行' if result.is_feasible else '不可行'
                    }
                    writer.writerow(row)
                    
            print(f"所有关卡汇总已导出: {file_path}")
            
        except Exception as e:
            print(f"导出所有关卡汇总失败: {e}")
    
    def export_excel_template(self, result, file_path: Path):
        """导出Excel模板格式 (符合题目要求)"""
        try:
            # 计算剩余资源
            initial_water = result.water_needed
            initial_food = result.food_needed
            initial_money = 10000 - result.purchase_cost
            
            remaining_water = initial_water
            remaining_food = initial_food
            remaining_money = initial_money
            
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as f:
                fieldnames = ['日期', '所在区域', '剩余资金数', '剩余水量', '剩余食物量']
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                
                for log in result.daily_log:
                    # 扣除消耗
                    remaining_water -= log.get('water_consumed', 0)
                    remaining_food -= log.get('food_consumed', 0)
                    remaining_money += log.get('income', 0)
                    
                    row = {
                        '日期': log.get('day', 0),
                        '所在区域': log.get('location', 0),
                        '剩余资金数': remaining_money,
                        '剩余水量': remaining_water,
                        '剩余食物量': remaining_food
                    }
                    writer.writerow(row)
                    
            print(f"Excel模板已导出: {file_path}")
            
        except Exception as e:
            print(f"导出Excel模板失败: {e}")
    
    def export_strategy_analysis(self, all_strategies: List[Dict], file_path: Path):
        """导出策略分析结果"""
        try:
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as f:
                fieldnames = [
                    '挖矿天数', '总用时', '最终资金', '净利润', '水量需求', 
                    '食物需求', '总收入', '购买成本', '可行性', '负重利用率'
                ]
                
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                
                for strategy in all_strategies:
                    if strategy:
                        weight_usage = ((strategy.get('water_needed', 0) * 3 + 
                                       strategy.get('food_needed', 0) * 2) / 1200 * 100)
                        
                        row = {
                            '挖矿天数': strategy.get('mining_days', 0),
                            '总用时': strategy.get('total_days', 0),
                            '最终资金': strategy.get('final_money', 0),
                            '净利润': strategy.get('final_money', 0) - 10000,
                            '水量需求': strategy.get('water_needed', 0),
                            '食物需求': strategy.get('food_needed', 0),
                            '总收入': strategy.get('total_income', 0),
                            '购买成本': strategy.get('purchase_cost', 0),
                            '可行性': '可行' if strategy.get('is_feasible', False) else '不可行',
                            '负重利用率': f"{weight_usage:.1f}%"
                        }
                        writer.writerow(row)
                        
            print(f"策略分析已导出: {file_path}")
            
        except Exception as e:
            print(f"导出策略分析失败: {e}")
    
    def create_report(self, results: Dict, output_dir: Path):
        """创建完整的分析报告"""
        try:
            report_file = output_dir / "analysis_report.md"
            
            with open(report_file, 'w', encoding='utf-8') as f:
                f.write("# 穿越沙漠问题求解报告\n\n")
                f.write(f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")
                
                f.write("## 求解结果汇总\n\n")
                f.write("| 关卡 | 挖矿天数 | 总用时 | 最终资金 | 净利润 | 可行性 |\n")
                f.write("|------|----------|--------|----------|--------|---------|\n")
                
                for level in sorted(results.keys()):
                    result = results[level]
                    f.write(f"| {level} | {result.mining_days} | {result.total_days} | "
                           f"{result.final_money} | {result.final_money - 10000} | "
                           f"{'✓' if result.is_feasible else '✗'} |\n")
                
                f.write("\n## 详细分析\n\n")
                
                for level in sorted(results.keys()):
                    result = results[level]
                    f.write(f"### 第{level}关\n\n")
                    f.write(f"- **最优策略**: 挖矿{result.mining_days}天\n")
                    f.write(f"- **总用时**: {result.total_days}天\n")
                    f.write(f"- **资源需求**: 水{result.water_needed}箱, 食物{result.food_needed}箱\n")
                    f.write(f"- **经济效益**: 收入{result.total_income}元, 成本{result.purchase_cost}元, 净利润{result.final_money - 10000}元\n")
                    
                    if hasattr(result, 'path') and result.path:
                        path_str = ' → '.join(map(str, result.path))
                        f.write(f"- **行程路径**: {path_str}\n")
                    
                    f.write("\n")
                
                f.write("## 结论\n\n")
                f.write("通过数学建模和算法优化，成功求解了所有关卡的最优策略。\n")
                f.write("结果表明，合理的挖矿天数规划和资源配置是获得最大收益的关键。\n")
                
            print(f"分析报告已生成: {report_file}")
            
        except Exception as e:
            print(f"生成分析报告失败: {e}")
