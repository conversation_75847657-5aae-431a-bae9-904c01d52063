#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
穿越沙漠数学模型完整演示程序
展示详细的数学建模过程和计算步骤
"""

import sys
import os
from pathlib import Path

# 添加当前目录到Python路径
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

try:
    from config.game_config import GameConfig
    from utils.mathematical_model_display import MathematicalModelDisplay
    from utils.calculation_process_display import CalculationProcessDisplay
    from utils.report_generator import ReportGenerator
    from solvers.level_solver import CorrectedLevelSolver
except ImportError as e:
    print(f"导入模块失败: {e}")
    exit(1)

class MathematicalModelDemo:
    """数学模型完整演示"""
    
    def __init__(self):
        self.config = GameConfig()
    
    def run_complete_demo(self, level: int):
        """运行完整的数学模型演示"""
        print("🎓 穿越沙漠问题数学建模完整演示")
        print("=" * 80)
        print(f"演示关卡: 第{level}关")
        print("=" * 80)

        # 获取关卡配置
        level_config = self.config.get_level_config(level)
        if not level_config:
            print(f"错误：未找到第{level}关的配置")
            return

        print(f"📊 正在分析第{level}关...")

        # 1. 求解问题获取结果
        solver = CorrectedLevelSolver(level_config)
        result = solver.solve()

        if result and result.is_feasible:
            print(f"✅ 求解成功！最优策略: {result.strategy_type}, 最终资金: {result.final_money:,}元")
        else:
            print("❌ 求解失败，将生成理论分析报告")
            result = None

        # 2. 生成详细报告文件
        report_generator = ReportGenerator(level_config, level)
        report_file = report_generator.save_report(f"level_{level}_detailed_report.txt", result)

        # 3. 导出JSON模型文档
        model_display = MathematicalModelDisplay(level_config)
        output_dir = Path("results")
        output_dir.mkdir(exist_ok=True)
        model_file = output_dir / f"level_{level}_mathematical_model.json"
        model_display.export_model_to_file(str(model_file))

        print(f"\n🎉 第{level}关分析完成！")
        print(f"📄 详细报告已保存到: {report_file}")
        print(f"📁 数学模型已保存到: {model_file}")

        return result
    
    def run_all_levels_demo(self):
        """运行所有关卡的数学模型演示"""
        print("🌟 所有关卡数学模型分析")
        print("=" * 80)

        results_summary = []

        for level in range(1, 7):
            print(f"\n{'='*20} 第{level}关 {'='*20}")
            result = self.run_complete_demo(level)

            if result:
                results_summary.append({
                    'level': level,
                    'strategy': result.strategy_type,
                    'final_money': result.final_money,
                    'net_profit': result.final_money - 10000,
                    'days': result.final_day
                })

        # 生成汇总报告
        self._generate_summary_report(results_summary)

        print(f"\n🎉 所有关卡分析完成！")
        print(f"📁 详细报告已保存到 results/ 目录")

    def _generate_summary_report(self, results_summary):
        """生成汇总报告"""
        output_dir = Path("results")
        output_dir.mkdir(exist_ok=True)

        summary_file = output_dir / "all_levels_summary_report.txt"

        with open(summary_file, 'w', encoding='utf-8') as f:
            f.write("=" * 80 + "\n")
            f.write("穿越沙漠问题 - 所有关卡汇总分析报告".center(80) + "\n")
            f.write("=" * 80 + "\n\n")

            f.write("关卡对比分析:\n")
            f.write("-" * 60 + "\n")
            f.write(f"{'关卡':<6} {'策略':<10} {'最终资金':<12} {'净利润':<12} {'用时':<6}\n")
            f.write("-" * 60 + "\n")

            total_profit = 0
            for result in results_summary:
                f.write(f"{result['level']:<6} {result['strategy']:<10} "
                       f"{result['final_money']:<12,} {result['net_profit']:<12,} "
                       f"{result['days']:<6}\n")
                total_profit += result['net_profit']

            f.write("-" * 60 + "\n")
            f.write(f"总净利润: {total_profit:,}元\n")
            f.write(f"平均净利润: {total_profit/len(results_summary):,.0f}元\n")

        print(f"📊 汇总报告已保存到: {summary_file}")
    


def main():
    """主函数"""
    print("🎓 穿越沙漠数学建模演示系统")
    print("=" * 60)
    
    demo = MathematicalModelDemo()
    
    print("\n请选择演示模式:")
    print("1. 单个关卡完整演示")
    print("2. 所有关卡对比演示")
    print("3. 数学模型理论介绍")
    
    try:
        choice = input("\n请输入选择 (1-3): ").strip()
        
        if choice == "1":
            level = int(input("请输入关卡号 (1-6): "))
            if 1 <= level <= 6:
                demo.run_complete_demo(level)
            else:
                print("错误：关卡号必须在1-6之间")
                
        elif choice == "2":
            demo.run_all_levels_demo()
            
        elif choice == "3":
            print("\n📚 数学建模理论介绍:")
            print("=" * 40)
            print("穿越沙漠问题是一个典型的:")
            print("• 动态规划问题 (天气已知情况)")
            print("• 随机规划问题 (天气未知情况)")
            print("• 多目标优化问题 (时间-收益权衡)")
            print("• 约束满足问题 (资源-负重约束)")
            print("• 博弈论问题 (多人竞争情况)")
            
            print("\n🔬 核心数学概念:")
            print("• 状态空间搜索")
            print("• 贝尔曼最优性原理")
            print("• 马尔可夫决策过程")
            print("• 蒙特卡洛模拟")
            print("• 纳什均衡 (多人博弈)")
            
        else:
            print("错误：无效的选择")
            
    except KeyboardInterrupt:
        print("\n\n程序被用户中断")
    except Exception as e:
        print(f"\n程序执行出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
