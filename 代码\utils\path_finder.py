#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
路径查找器
实现最短路径算法，找到从起点到终点经过矿山的最优路径
"""

from typing import Dict, List, Optional, Tuple
from collections import deque
import heapq

try:
    from config.game_config import LevelConfig
except ImportError as e:
    print(f"导入配置模块失败: {e}")
    raise

class PathFinder:
    """路径查找器"""
    
    def __init__(self, config: LevelConfig):
        self.config = config
        # 为了向后兼容，添加mine_node属性
        if hasattr(config, 'mine_nodes') and config.mine_nodes:
            self.config.mine_node = config.mine_nodes[0]
        elif not hasattr(config, 'mine_node'):
            self.config.mine_node = None
        
    def find_optimal_path(self) -> Optional[List[int]]:
        """找到最优路径：起点 -> 矿山 -> 终点"""

        # 获取第一个矿山节点（兼容单个和多个矿山）
        mine_node = self.config.mine_nodes[0] if self.config.mine_nodes else None
        if mine_node is None:
            print("错误：未找到矿山节点")
            return None

        # 找到起点到矿山的最短路径
        path_to_mine = self._find_shortest_path(
            self.config.start_node,
            mine_node
        )

        if not path_to_mine:
            print("错误：无法找到从起点到矿山的路径")
            return None

        # 找到矿山到终点的最短路径
        path_from_mine = self._find_shortest_path(
            mine_node,
            self.config.end_node
        )

        if not path_from_mine:
            print("错误：无法找到从矿山到终点的路径")
            return None

        # 合并路径，避免重复矿山节点
        full_path = path_to_mine + path_from_mine[1:]

        print(f"找到完整路径: {' -> '.join(map(str, full_path))}")
        print(f"路径长度: {len(full_path) - 1}天")

        return full_path
    
    def _find_shortest_path(self, start: int, end: int) -> Optional[List[int]]:
        """使用BFS找到两点间的最短路径"""
        if start == end:
            return [start]
        
        # BFS队列：(当前节点, 路径)
        queue = deque([(start, [start])])
        visited = {start}
        
        while queue:
            current_node, path = queue.popleft()
            
            # 检查所有相邻节点
            for neighbor in self.config.node_connections.get(current_node, []):
                if neighbor == end:
                    return path + [neighbor]
                
                if neighbor not in visited:
                    visited.add(neighbor)
                    queue.append((neighbor, path + [neighbor]))
        
        return None
    
    def find_path_with_villages(self) -> Optional[List[int]]:
        """找到经过村庄的路径 (用于第二关等有村庄的关卡)"""
        if not self.config.village_nodes:
            return self.find_optimal_path()
        
        best_path = None
        min_length = float('inf')
        
        # 获取第一个矿山节点
        mine_node = self.config.mine_nodes[0] if self.config.mine_nodes else None
        if mine_node is None:
            return self.find_optimal_path()

        # 尝试不同的村庄访问顺序
        for village in self.config.village_nodes:
            # 起点 -> 村庄 -> 矿山 -> 终点
            path1 = self._find_shortest_path(self.config.start_node, village)
            path2 = self._find_shortest_path(village, mine_node)
            path3 = self._find_shortest_path(mine_node, self.config.end_node)
            
            if path1 and path2 and path3:
                full_path = path1 + path2[1:] + path3[1:]
                if len(full_path) < min_length:
                    min_length = len(full_path)
                    best_path = full_path
        
        # 也考虑不经过村庄的直接路径
        direct_path = self.find_optimal_path()
        if direct_path and len(direct_path) < min_length:
            best_path = direct_path
        
        return best_path
    
    def find_all_possible_paths(self, max_length: int = 20) -> List[List[int]]:
        """找到所有可能的路径 (用于复杂优化)"""
        all_paths = []
        
        def dfs(current: int, target: int, path: List[int], visited: set):
            if len(path) > max_length:
                return
            
            if current == target:
                all_paths.append(path.copy())
                return
            
            for neighbor in self.config.node_connections.get(current, []):
                if neighbor not in visited:
                    visited.add(neighbor)
                    path.append(neighbor)
                    dfs(neighbor, target, path, visited)
                    path.pop()
                    visited.remove(neighbor)
        
        # 获取第一个矿山节点
        mine_node = self.config.mine_nodes[0] if self.config.mine_nodes else None
        if mine_node is None:
            return []

        # 找到所有从起点到矿山的路径
        paths_to_mine = []
        dfs(self.config.start_node, mine_node, [self.config.start_node], {self.config.start_node})
        paths_to_mine = all_paths.copy()

        # 找到所有从矿山到终点的路径
        all_paths.clear()
        dfs(mine_node, self.config.end_node, [mine_node], {mine_node})
        paths_from_mine = all_paths.copy()
        
        # 组合所有可能的完整路径
        complete_paths = []
        for path1 in paths_to_mine:
            for path2 in paths_from_mine:
                complete_path = path1 + path2[1:]  # 避免重复矿山节点
                complete_paths.append(complete_path)
        
        return complete_paths
    
    def calculate_path_cost(self, path: List[int], weather_forecast: List[str]) -> int:
        """计算路径的总消耗成本 (考虑天气)"""
        total_cost = 0
        
        for day, node in enumerate(path[:-1]):  # 不包括最后一个节点
            if day < len(weather_forecast):
                weather = weather_forecast[day]
                # 这里需要根据天气计算移动成本
                # 简化处理：不同天气的基础移动成本
                if weather == '晴朗':
                    cost = 1
                elif weather == '高温':
                    cost = 2
                else:  # 沙暴
                    cost = 3
                total_cost += cost
        
        return total_cost
    
    def get_path_info(self, path: List[int]) -> Dict:
        """获取路径信息"""
        if not path:
            return {}

        # 获取第一个矿山节点的位置
        mine_position = -1
        if self.config.mine_nodes:
            for mine_node in self.config.mine_nodes:
                if mine_node in path:
                    mine_position = path.index(mine_node)
                    break

        return {
            'path': path,
            'length': len(path) - 1,  # 天数
            'nodes': len(path),
            'start': path[0],
            'end': path[-1],
            'mine_position': mine_position,
            'mines_visited': [node for node in path if node in self.config.mine_nodes],
            'villages_visited': [node for node in path if node in self.config.village_nodes]
        }
