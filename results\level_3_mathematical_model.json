{"level": 3, "problem_type": "穿越沙漠优化问题", "model_type": "混合整数规划 (MIP)", "variables": {"x_ij^t": "第t天从节点i移动到节点j的决策变量 (0或1)", "s_i^t": "第t天在节点i停留的决策变量 (0或1)", "m_i^t": "第t天在矿山i挖矿的决策变量 (0或1)", "w_0": "初始购买的水量 (箱)", "f_0": "初始购买的食物量 (箱)", "w_t": "第t天结束时的水量 (箱)", "f_t": "第t天结束时的食物量 (箱)", "M_t": "第t天结束时的资金 (元)"}, "parameters": {"T": "时间限制 = 30天", "W_max": "负重上限 = 1200kg", "M_0": "初始资金 = 10000元", "P_w": "水价格 = 5元/箱", "P_f": "食物价格 = 10元/箱", "C_w^base": "基础水消耗 = 3箱/天", "C_f^base": "基础食物消耗 = 4箱/天", "α": "移动消耗倍数 = 2.0", "β": "挖矿消耗倍数 = 1.5", "R": "挖矿收益 = 1000元/天"}, "constraints": ["初始资金约束: P_w × w_0 + P_f × f_0 ≤ M_0", "负重约束: w_t + f_t ≤ W_max, ∀t ∈ [0,T]", "时间约束: ∑(x_ij^t + s_i^t) = 1, ∀t ∈ [1,T]", "路径连续性: ∑_j x_ji^t = ∑_j x_ij^(t+1) + s_i^(t+1), ∀i,t", "资源消耗约束: w_(t+1) = w_t - C_w(action_t, weather_t)", "非负约束: w_t ≥ 0, f_t ≥ 0, ∀t", "沙暴约束: 沙暴日必须停留, x_ij^t = 0 if weather_t = '沙暴'", "终点约束: 必须在截止日期前到达终点"], "objective": "max Z = M_T + 0.5 × P_w × w_T + 0.5 × P_f × f_T", "solution_method": "贪心算法 + 蒙特卡洛模拟 (天气未知)", "complexity_analysis": {"time_complexity": "O(T × N)", "space_complexity": "O(N)", "variables_count": "约 540个", "constraints_count": "约 240个"}}