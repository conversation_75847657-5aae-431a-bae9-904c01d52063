#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数据处理器
处理输入数据和结果数据
"""

import json
import csv
from typing import Dict, List, Any
from pathlib import Path

class DataProcessor:
    """数据处理器"""
    
    def __init__(self):
        pass
    
    def load_weather_data(self, file_path: str) -> List[str]:
        """从文件加载天气数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                if file_path.endswith('.json'):
                    data = json.load(f)
                    return data.get('weather', [])
                elif file_path.endswith('.csv'):
                    reader = csv.reader(f)
                    weather_data = []
                    for row in reader:
                        if row:
                            weather_data.extend(row)
                    return weather_data
                else:
                    # 纯文本文件，每行一个天气
                    return [line.strip() for line in f.readlines()]
        except Exception as e:
            print(f"加载天气数据失败: {e}")
            return []
    
    def load_map_data(self, file_path: str) -> Dict[int, List[int]]:
        """从文件加载地图连接数据"""
        try:
            with open(file_path, 'r', encoding='utf-8') as f:
                data = json.load(f)
                # 将字符串键转换为整数键
                return {int(k): v for k, v in data.items()}
        except Exception as e:
            print(f"加载地图数据失败: {e}")
            return {}
    
    def save_result_to_json(self, result: Dict, file_path: str):
        """保存结果到JSON文件"""
        try:
            with open(file_path, 'w', encoding='utf-8') as f:
                json.dump(result, f, ensure_ascii=False, indent=2)
            print(f"结果已保存到: {file_path}")
        except Exception as e:
            print(f"保存JSON结果失败: {e}")
    
    def save_result_to_csv(self, daily_log: List[Dict], file_path: str):
        """保存每日日志到CSV文件"""
        if not daily_log:
            return
        
        try:
            with open(file_path, 'w', newline='', encoding='utf-8') as f:
                fieldnames = daily_log[0].keys()
                writer = csv.DictWriter(f, fieldnames=fieldnames)
                writer.writeheader()
                writer.writerows(daily_log)
            print(f"每日日志已保存到: {file_path}")
        except Exception as e:
            print(f"保存CSV结果失败: {e}")
    
    def validate_config(self, config: Dict) -> bool:
        """验证配置数据的完整性"""
        required_fields = [
            'level', 'players', 'max_weight', 'initial_money', 
            'deadline_days', 'base_income'
        ]
        
        for field in required_fields:
            if field not in config:
                print(f"配置缺少必要字段: {field}")
                return False
        
        return True
    
    def format_result_for_display(self, result: Dict) -> str:
        """格式化结果用于显示"""
        if not result:
            return "无有效结果"
        
        lines = [
            f"=== 第{result.get('level', '?')}关求解结果 ===",
            f"最优挖矿天数: {result.get('mining_days', 0)}天",
            f"总用时: {result.get('total_days', 0)}天",
            f"最终资金: {result.get('final_money', 0)}元",
            f"所需水量: {result.get('water_needed', 0)}箱",
            f"所需食物: {result.get('food_needed', 0)}箱",
            f"总收入: {result.get('total_income', 0)}元",
            f"购买成本: {result.get('purchase_cost', 0)}元",
            f"净利润: {result.get('final_money', 0) - 10000}元"
        ]
        
        if 'path' in result:
            path_str = ' -> '.join(map(str, result['path']))
            lines.append(f"行程路径: {path_str}")
        
        return '\n'.join(lines)
    
    def create_summary_table(self, results: Dict[int, Dict]) -> List[List]:
        """创建汇总表格数据"""
        headers = [
            '关卡', '挖矿天数', '总用时', '最终资金', '净利润', 
            '水量', '食物量', '总收入', '购买成本'
        ]
        
        rows = [headers]
        
        for level in sorted(results.keys()):
            result = results[level]
            row = [
                level,
                result.get('mining_days', 0),
                result.get('total_days', 0),
                result.get('final_money', 0),
                result.get('final_money', 0) - 10000,
                result.get('water_needed', 0),
                result.get('food_needed', 0),
                result.get('total_income', 0),
                result.get('purchase_cost', 0)
            ]
            rows.append(row)
        
        return rows
    
    def export_excel_format(self, daily_log: List[Dict], file_path: str):
        """导出Excel格式的每日记录"""
        try:
            # 创建符合题目要求的格式
            excel_data = []
            
            for log in daily_log:
                excel_row = {
                    '日期': log.get('day', 0),
                    '所在区域': log.get('location', 0),
                    '剩余资金数': 0,  # 需要累计计算
                    '剩余水量': 0,    # 需要累计计算
                    '剩余食物量': 0   # 需要累计计算
                }
                excel_data.append(excel_row)
            
            # 保存为CSV格式 (可以用Excel打开)
            with open(file_path, 'w', newline='', encoding='utf-8-sig') as f:
                if excel_data:
                    fieldnames = excel_data[0].keys()
                    writer = csv.DictWriter(f, fieldnames=fieldnames)
                    writer.writeheader()
                    writer.writerows(excel_data)
            
            print(f"Excel格式数据已保存到: {file_path}")
            
        except Exception as e:
            print(f"导出Excel格式失败: {e}")
    
    def calculate_remaining_resources(self, daily_log: List[Dict], 
                                    initial_water: int, initial_food: int, 
                                    initial_money: int) -> List[Dict]:
        """计算每日剩余资源"""
        remaining_water = initial_water
        remaining_food = initial_food
        remaining_money = initial_money
        
        updated_log = []
        
        for log in daily_log:
            # 扣除当日消耗
            remaining_water -= log.get('water_consumed', 0)
            remaining_food -= log.get('food_consumed', 0)
            
            # 增加当日收入
            remaining_money += log.get('income', 0)
            
            # 更新日志
            updated_entry = log.copy()
            updated_entry.update({
                'remaining_water': remaining_water,
                'remaining_food': remaining_food,
                'remaining_money': remaining_money
            })
            
            updated_log.append(updated_entry)
        
        return updated_log
