#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
穿越沙漠数学建模项目 - 简化版数据可视化
"""

import json
import pandas as pd
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import numpy as np
import os
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_summary_data():
    """加载汇总数据"""
    results_dir = Path('../results')
    summaries = {}
    
    for level in range(1, 7):
        summary_file = results_dir / f'level_{level}_summary.json'
        if summary_file.exists():
            try:
                with open(summary_file, 'r', encoding='utf-8') as f:
                    summaries[level] = json.load(f)
            except Exception as e:
                print(f"加载关卡{level}数据失败: {e}")
    
    return summaries

def create_basic_comparison_chart():
    """创建基础对比图表"""
    print("生成基础对比图表...")
    
    # 创建输出目录
    output_dir = Path('charts')
    output_dir.mkdir(exist_ok=True)
    
    # 加载数据
    summaries = load_summary_data()
    
    if not summaries:
        print("没有找到数据文件")
        return
    
    # 准备数据
    levels = []
    net_profits = []
    final_money = []
    days_used = []
    
    for level in range(1, 7):
        if level in summaries:
            data = summaries[level]
            levels.append(f'关卡{level}')
            net_profits.append(data.get('net_profit', 0))
            final_money.append(data.get('final_money', 0))
            days_used.append(data.get('final_day', 0))
    
    # 创建图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('穿越沙漠问题 - 各关卡对比分析', fontsize=16, fontweight='bold')
    
    # 1. 净利润对比
    colors = ['green' if x > 0 else 'red' for x in net_profits]
    bars1 = ax1.bar(levels, net_profits, color=colors, alpha=0.7)
    ax1.set_title('各关卡净利润对比', fontweight='bold')
    ax1.set_ylabel('净利润 (元)')
    ax1.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    ax1.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, value in zip(bars1, net_profits):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., height + (50 if height >= 0 else -200),
                f'{value:,}', ha='center', va='bottom' if height >= 0 else 'top')
    
    # 2. 最终资金对比
    bars2 = ax2.bar(levels, final_money, color='steelblue', alpha=0.7)
    ax2.set_title('各关卡最终资金对比', fontweight='bold')
    ax2.set_ylabel('最终资金 (元)')
    ax2.grid(True, alpha=0.3)
    
    for bar, value in zip(bars2, final_money):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 100,
                f'{value:,}', ha='center', va='bottom')
    
    # 3. 用时对比
    bars3 = ax3.bar(levels, days_used, color='orange', alpha=0.7)
    ax3.set_title('各关卡用时对比', fontweight='bold')
    ax3.set_ylabel('用时 (天)')
    ax3.grid(True, alpha=0.3)
    
    for bar, value in zip(bars3, days_used):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 0.2,
                f'{value}', ha='center', va='bottom')
    
    # 4. 趋势线图
    ax4.plot(range(1, len(levels)+1), net_profits, marker='o', linewidth=2, markersize=8, color='green')
    ax4.fill_between(range(1, len(levels)+1), net_profits, alpha=0.3, color='green')
    ax4.set_title('净利润变化趋势', fontweight='bold')
    ax4.set_xlabel('关卡')
    ax4.set_ylabel('净利润 (元)')
    ax4.grid(True, alpha=0.3)
    ax4.axhline(y=0, color='red', linestyle='--', alpha=0.7)
    ax4.set_xticks(range(1, len(levels)+1))
    ax4.set_xticklabels([f'关卡{i}' for i in range(1, len(levels)+1)])
    
    plt.tight_layout()
    plt.savefig(output_dir / 'basic_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✓ 基础对比图表已保存到: {output_dir / 'basic_comparison.png'}")

def create_summary_table():
    """创建汇总表格"""
    print("生成汇总表格...")
    
    output_dir = Path('charts')
    output_dir.mkdir(exist_ok=True)
    
    summaries = load_summary_data()
    
    if not summaries:
        print("没有找到数据文件")
        return
    
    # 准备表格数据
    table_data = []
    headers = ['关卡', '策略类型', '最终资金', '净利润', '用时', '状态']
    
    total_profit = 0
    for level in range(1, 7):
        if level in summaries:
            data = summaries[level]
            strategy = data.get('strategy_type', 'N/A')
            final_money = data.get('final_money', 0)
            net_profit = data.get('net_profit', 0)
            days = data.get('final_day', 0)
            status = "盈利" if net_profit > 0 else "亏损"
            
            total_profit += net_profit
            
            table_data.append([
                f'关卡{level}',
                strategy,
                f'{final_money:,}',
                f'{net_profit:,}',
                f'{days}',
                status
            ])
    
    # 创建表格图
    fig, ax = plt.subplots(figsize=(12, 8))
    fig.suptitle('穿越沙漠问题 - 结果汇总表', fontsize=16, fontweight='bold')
    
    ax.axis('tight')
    ax.axis('off')
    
    table = ax.table(cellText=table_data, colLabels=headers, 
                    cellLoc='center', loc='center')
    
    table.auto_set_font_size(False)
    table.set_fontsize(10)
    table.scale(1.2, 2)
    
    # 设置表格样式
    for i in range(len(headers)):
        table[(0, i)].set_facecolor('#4CAF50')
        table[(0, i)].set_text_props(weight='bold', color='white')
    
    for i in range(1, len(table_data) + 1):
        for j in range(len(headers)):
            if i % 2 == 0:
                table[(i, j)].set_facecolor('#f0f0f0')
            # 根据盈亏状态设置颜色
            if j == 5:  # 状态列
                if table_data[i-1][j] == '盈利':
                    table[(i, j)].set_facecolor('#c8e6c9')
                else:
                    table[(i, j)].set_facecolor('#ffcdd2')
    
    # 添加汇总信息
    summary_text = f'总净利润: {total_profit:,} 元\n平均净利润: {total_profit/len(summaries):,.0f} 元'
    plt.figtext(0.5, 0.02, summary_text, ha='center', fontsize=12, 
                bbox=dict(boxstyle="round,pad=0.3", facecolor="lightblue"))
    
    plt.savefig(output_dir / 'summary_table.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✓ 汇总表格已保存到: {output_dir / 'summary_table.png'}")

def main():
    """主函数"""
    print("穿越沙漠数学建模项目 - 简化版数据可视化")
    print("="*50)
    
    try:
        create_basic_comparison_chart()
        create_summary_table()
        
        print("="*50)
        print("✅ 图表生成完成！")
        print("📁 输出目录: charts/")
        
        # 列出生成的文件
        output_dir = Path('charts')
        if output_dir.exists():
            print("\n生成的文件:")
            for file in output_dir.glob('*.png'):
                print(f"  - {file.name}")
        
    except Exception as e:
        print(f"❌ 生成图表时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
