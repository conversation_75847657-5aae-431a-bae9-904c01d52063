{"level": 6, "problem_type": "随机性MINLP", "variables": {"spatial": "x_ij^t, s_i^t, l_i^t for i,j ∈ {1,...,25}", "resource": "w_t, f_t, w_0, f_0", "action": "m_i^t, b_i^t", "state": "M_t, arrived_t"}, "parameters": {"nodes": 25, "time_limit": 30, "initial_money": 10000, "players": 3, "weather_known": false}, "objective": "max Z = M_T + 0.5·P_w·w_T + 0.5·P_f·f_T", "solution_method": "贪心策略", "complexity": "O(K·T·N)"}