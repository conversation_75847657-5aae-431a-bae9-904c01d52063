# 穿越沙漠数学模型详细展示系统

## 📋 概述

本系统为穿越沙漠数学建模问题提供了完整的数学模型展示和详细计算过程演示功能。通过可视化的方式展示：

- 🔢 完整的数学模型建立过程
- 🧮 详细的计算步骤和公式推导
- 📊 约束条件的数学表达
- 🎯 目标函数的优化过程
- 🔍 算法原理和实现细节

## 🚀 快速开始

### 方法1: 一键演示（推荐）
```bash
cd 代码
python3 run_mathematical_demo.py
```
选择选项1进行快速完整演示。

### 方法2: 完整演示程序
```bash
cd 代码
python3 mathematical_model_demo.py
```

### 方法3: 单独运行各个模块
```bash
# 数学模型展示
python3 -c "from utils.mathematical_model_display import *; demonstrate_mathematical_modeling()"

# 计算过程展示
python3 -c "from utils.calculation_process_display import *; demonstrate_calculation_process()"
```

## 📁 新增文件说明

### 核心模块

1. **`utils/mathematical_model_display.py`**
   - 数学模型完整展示器
   - 决策变量、参数、约束条件定义
   - 目标函数和求解方法展示
   - 算法复杂度分析

2. **`utils/calculation_process_display.py`**
   - 详细计算过程展示器
   - 每步计算的数学公式
   - 资源消耗、收益计算
   - 策略对比分析

3. **`mathematical_model_demo.py`**
   - 完整演示程序主入口
   - 整合所有展示功能
   - 支持单关卡和全关卡演示

4. **`run_mathematical_demo.py`**
   - 快速演示启动器
   - 简化的用户界面
   - 多种演示模式选择

## 🔢 数学模型核心内容

### 决策变量
- `x_ij^t`: 第t天从节点i移动到节点j的决策变量
- `s_i^t`: 第t天在节点i停留的决策变量
- `m_i^t`: 第t天在矿山i挖矿的决策变量
- `w_t, f_t`: 第t天的水量和食物量
- `M_t`: 第t天的资金

### 约束条件
1. **资金约束**: 初始购买不超过预算
2. **负重约束**: 总重量不超过负重上限
3. **时间约束**: 必须在截止日期前到达
4. **资源约束**: 水和食物不能耗尽
5. **路径约束**: 移动路径必须连续
6. **天气约束**: 沙暴日必须停留

### 目标函数
```
max Z = M_T + 0.5 × P_w × w_T + 0.5 × P_f × f_T
```
最大化最终资金，包括剩余资源的退回价值。

## 🧮 详细计算展示

### 1. 初始资源购买计算
- 水和食物的购买数量优化
- 费用计算和约束检查
- 负重和资金约束验证

### 2. 每日消耗计算
- 基础消耗量计算
- 行动类型影响（停留/移动/挖矿）
- 天气因素影响（晴朗/高温/沙暴）
- 剩余资源更新

### 3. 挖矿收益计算
- 单人挖矿收益
- 多人博弈下的收益分摊
- 挖矿天数优化

### 4. 路径优化计算
- 最短路径算法
- 路径资源成本计算
- 时间效率分析

### 5. 最终利润计算
- 剩余资源退回价值
- 净利润和投资回报率
- 策略效果评估

## 📊 算法详解

### 动态规划算法（天气已知）
- **状态定义**: State(t, i, w, f, m)
- **状态转移**: 考虑所有可能行动
- **最优子结构**: 贝尔曼最优性原理
- **复杂度**: O(T × N × W × F × M)

### 贪心算法（天气未知）
- **贪心策略**: 最大化即时收益
- **风险评估**: 预留安全资源
- **动态调整**: 根据天气变化调整策略
- **复杂度**: O(T × N)

## 🎯 使用场景

### 1. 学习理解
- 理解数学建模的完整过程
- 学习动态规划和贪心算法
- 掌握约束优化问题的建模方法

### 2. 结果验证
- 验证求解结果的正确性
- 理解每一步计算的依据
- 分析不同策略的优劣

### 3. 模型改进
- 识别模型的改进空间
- 测试新的约束条件
- 优化算法性能

## 📈 输出文件

运行演示后会生成以下文件：

1. **`results/level_X_mathematical_model.json`**
   - 完整的数学模型定义
   - JSON格式，便于程序处理

2. **控制台输出**
   - 详细的计算过程
   - 公式推导步骤
   - 结果分析

## 🔧 扩展功能

### 自定义演示
可以修改演示参数来测试不同场景：

```python
# 修改初始资源
calc_display.display_initial_resource_calculation(30, 25)

# 修改天气条件
calc_display.display_daily_consumption_calculation(
    day=1, action="移动", weather="高温", 
    current_water=30, current_food=25
)
```

### 添加新的展示内容
可以扩展现有模块，添加更多展示功能：

- 敏感性分析
- 参数优化过程
- 蒙特卡洛模拟结果
- 博弈论分析

## 🎓 教学价值

本系统特别适合：

1. **数学建模课程**: 展示完整的建模过程
2. **算法学习**: 理解动态规划和贪心算法
3. **优化理论**: 学习约束优化问题
4. **程序设计**: 学习模块化设计和代码组织

## 📞 使用帮助

如果在使用过程中遇到问题：

1. 检查Python环境（需要Python 3.6+）
2. 确保所有模块文件都在正确位置
3. 查看控制台错误信息
4. 检查results目录的写入权限

## 🎉 总结

通过这个数学模型展示系统，您可以：

- ✅ 完整理解穿越沙漠问题的数学建模过程
- ✅ 掌握每一步计算的数学原理
- ✅ 学习动态规划和贪心算法的实现
- ✅ 验证和分析求解结果的正确性
- ✅ 为进一步的模型改进提供基础

希望这个系统能帮助您更好地理解和掌握数学建模的精髓！
