#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
穿越沙漠数学建模项目 - 数据可视化生成器
生成各种统计图表和分析图表
"""

import json
import pandas as pd
import matplotlib.pyplot as plt
import seaborn as sns
import numpy as np
import os
from pathlib import Path
import warnings
warnings.filterwarnings('ignore')

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

class DesertCrossingVisualizer:
    def __init__(self, results_dir='../results'):
        self.results_dir = Path(results_dir)
        self.output_dir = Path('charts')
        self.output_dir.mkdir(exist_ok=True)
        
        # 数据存储
        self.level_summaries = {}
        self.daily_logs = {}
        self.mathematical_models = {}
        
        # 加载数据
        self.load_all_data()
    
    def load_all_data(self):
        """加载所有关卡的数据"""
        print("正在加载数据...")
        
        for level in range(1, 7):
            # 加载汇总数据
            summary_file = self.results_dir / f'level_{level}_summary.json'
            if summary_file.exists():
                with open(summary_file, 'r', encoding='utf-8') as f:
                    self.level_summaries[level] = json.load(f)
            
            # 加载每日日志
            log_file = self.results_dir / f'level_{level}_daily_log.csv'
            if log_file.exists():
                self.daily_logs[level] = pd.read_csv(log_file)
            
            # 加载数学模型
            model_file = self.results_dir / f'level_{level}_mathematical_model.json'
            if model_file.exists():
                with open(model_file, 'r', encoding='utf-8') as f:
                    self.mathematical_models[level] = json.load(f)
        
        print(f"成功加载 {len(self.level_summaries)} 个关卡的数据")
    
    def create_level_comparison_charts(self):
        """创建关卡对比图表"""
        print("生成关卡对比图表...")
        
        # 准备数据
        levels = []
        net_profits = []
        final_money = []
        days_used = []
        strategy_types = []
        
        for level in range(1, 7):
            if level in self.level_summaries:
                data = self.level_summaries[level]
                levels.append(f'关卡{level}')
                net_profits.append(data.get('net_profit', 0))
                final_money.append(data.get('final_money', 0))
                days_used.append(data.get('final_day', 0))
                strategy_types.append(data.get('strategy_type', 'unknown'))
        
        # 创建子图
        fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('穿越沙漠问题 - 各关卡综合对比分析', fontsize=16, fontweight='bold')
        
        # 1. 净利润对比
        colors = ['green' if x > 0 else 'red' for x in net_profits]
        bars1 = ax1.bar(levels, net_profits, color=colors, alpha=0.7)
        ax1.set_title('各关卡净利润对比', fontweight='bold')
        ax1.set_ylabel('净利润 (元)')
        ax1.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        ax1.grid(True, alpha=0.3)
        
        # 添加数值标签
        for bar, value in zip(bars1, net_profits):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + (50 if height >= 0 else -200),
                    f'{value:,}', ha='center', va='bottom' if height >= 0 else 'top')
        
        # 2. 最终资金对比
        bars2 = ax2.bar(levels, final_money, color='steelblue', alpha=0.7)
        ax2.set_title('各关卡最终资金对比', fontweight='bold')
        ax2.set_ylabel('最终资金 (元)')
        ax2.grid(True, alpha=0.3)
        
        for bar, value in zip(bars2, final_money):
            height = bar.get_height()
            ax2.text(bar.get_x() + bar.get_width()/2., height + 100,
                    f'{value:,}', ha='center', va='bottom')
        
        # 3. 用时对比
        bars3 = ax3.bar(levels, days_used, color='orange', alpha=0.7)
        ax3.set_title('各关卡用时对比', fontweight='bold')
        ax3.set_ylabel('用时 (天)')
        ax3.grid(True, alpha=0.3)
        
        for bar, value in zip(bars3, days_used):
            height = bar.get_height()
            ax3.text(bar.get_x() + bar.get_width()/2., height + 0.2,
                    f'{value}', ha='center', va='bottom')
        
        # 4. 策略类型分布
        strategy_counts = pd.Series(strategy_types).value_counts()
        colors_pie = ['lightcoral', 'lightblue', 'lightgreen', 'gold']
        wedges, texts, autotexts = ax4.pie(strategy_counts.values, labels=strategy_counts.index, 
                                          autopct='%1.1f%%', colors=colors_pie[:len(strategy_counts)])
        ax4.set_title('策略类型分布', fontweight='bold')
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'level_comparison.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✓ 关卡对比图表已生成")
    
    def create_trend_analysis_charts(self):
        """创建趋势分析图表"""
        print("生成趋势分析图表...")
        
        fig, axes = plt.subplots(2, 3, figsize=(18, 12))
        fig.suptitle('穿越沙漠问题 - 关卡趋势分析', fontsize=16, fontweight='bold')
        
        # 准备数据
        levels = list(range(1, 7))
        net_profits = [self.level_summaries.get(i, {}).get('net_profit', 0) for i in levels]
        final_money = [self.level_summaries.get(i, {}).get('final_money', 0) for i in levels]
        days_used = [self.level_summaries.get(i, {}).get('final_day', 0) for i in levels]
        
        # 1. 净利润趋势线
        axes[0,0].plot(levels, net_profits, marker='o', linewidth=2, markersize=8, color='green')
        axes[0,0].fill_between(levels, net_profits, alpha=0.3, color='green')
        axes[0,0].set_title('净利润变化趋势', fontweight='bold')
        axes[0,0].set_xlabel('关卡')
        axes[0,0].set_ylabel('净利润 (元)')
        axes[0,0].grid(True, alpha=0.3)
        axes[0,0].axhline(y=0, color='red', linestyle='--', alpha=0.7)
        
        # 2. 最终资金趋势线
        axes[0,1].plot(levels, final_money, marker='s', linewidth=2, markersize=8, color='blue')
        axes[0,1].fill_between(levels, final_money, alpha=0.3, color='blue')
        axes[0,1].set_title('最终资金变化趋势', fontweight='bold')
        axes[0,1].set_xlabel('关卡')
        axes[0,1].set_ylabel('最终资金 (元)')
        axes[0,1].grid(True, alpha=0.3)
        
        # 3. 用时趋势线
        axes[0,2].plot(levels, days_used, marker='^', linewidth=2, markersize=8, color='orange')
        axes[0,2].fill_between(levels, days_used, alpha=0.3, color='orange')
        axes[0,2].set_title('用时变化趋势', fontweight='bold')
        axes[0,2].set_xlabel('关卡')
        axes[0,2].set_ylabel('用时 (天)')
        axes[0,2].grid(True, alpha=0.3)
        
        # 4. 效率分析 (净利润/天)
        efficiency = [p/d if d > 0 else 0 for p, d in zip(net_profits, days_used)]
        axes[1,0].bar(levels, efficiency, color='purple', alpha=0.7)
        axes[1,0].set_title('日均收益效率', fontweight='bold')
        axes[1,0].set_xlabel('关卡')
        axes[1,0].set_ylabel('日均净利润 (元/天)')
        axes[1,0].grid(True, alpha=0.3)
        
        # 5. 投资回报率
        roi = [(f-10000)/10000*100 for f in final_money]
        colors_roi = ['green' if x > 0 else 'red' for x in roi]
        axes[1,1].bar(levels, roi, color=colors_roi, alpha=0.7)
        axes[1,1].set_title('投资回报率', fontweight='bold')
        axes[1,1].set_xlabel('关卡')
        axes[1,1].set_ylabel('ROI (%)')
        axes[1,1].axhline(y=0, color='black', linestyle='--', alpha=0.5)
        axes[1,1].grid(True, alpha=0.3)
        
        # 6. 综合评分 (标准化后的综合指标)
        # 标准化各指标
        norm_profit = [(p - min(net_profits))/(max(net_profits) - min(net_profits)) if max(net_profits) != min(net_profits) else 0.5 for p in net_profits]
        norm_time = [1 - (d - min(days_used))/(max(days_used) - min(days_used)) if max(days_used) != min(days_used) else 0.5 for d in days_used]  # 时间越少越好
        
        composite_score = [(p + t)/2 * 100 for p, t in zip(norm_profit, norm_time)]
        axes[1,2].bar(levels, composite_score, color='gold', alpha=0.7)
        axes[1,2].set_title('综合评分', fontweight='bold')
        axes[1,2].set_xlabel('关卡')
        axes[1,2].set_ylabel('综合评分')
        axes[1,2].grid(True, alpha=0.3)
        
        plt.tight_layout()
        plt.savefig(self.output_dir / 'trend_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()
        
        print("✓ 趋势分析图表已生成")

    def create_resource_analysis_charts(self):
        """创建资源消耗分析图表"""
        print("生成资源消耗分析图表...")

        # 选择有详细日志的关卡进行分析
        selected_levels = [level for level in [2, 4, 6] if level in self.daily_logs]

        if not selected_levels:
            print("⚠ 没有找到详细的日志数据，跳过资源分析")
            return

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('穿越沙漠问题 - 资源消耗分析', fontsize=16, fontweight='bold')

        # 1. 每日水消耗对比
        ax1 = axes[0, 0]
        for level in selected_levels:
            if level in self.daily_logs:
                df = self.daily_logs[level]
                if 'water_consumed' in df.columns:
                    ax1.plot(df['day'], df['water_consumed'],
                            marker='o', label=f'关卡{level}', linewidth=2)

        ax1.set_title('每日水消耗对比', fontweight='bold')
        ax1.set_xlabel('天数')
        ax1.set_ylabel('水消耗 (箱)')
        ax1.legend()
        ax1.grid(True, alpha=0.3)

        # 2. 每日食物消耗对比
        ax2 = axes[0, 1]
        for level in selected_levels:
            if level in self.daily_logs:
                df = self.daily_logs[level]
                if 'food_consumed' in df.columns:
                    ax2.plot(df['day'], df['food_consumed'],
                            marker='s', label=f'关卡{level}', linewidth=2)

        ax2.set_title('每日食物消耗对比', fontweight='bold')
        ax2.set_xlabel('天数')
        ax2.set_ylabel('食物消耗 (箱)')
        ax2.legend()
        ax2.grid(True, alpha=0.3)

        # 3. 天气类型对消耗的影响
        ax3 = axes[1, 0]
        weather_consumption = {}

        for level in selected_levels:
            if level in self.daily_logs:
                df = self.daily_logs[level]
                if all(col in df.columns for col in ['weather', 'water_consumed', 'food_consumed']):
                    for _, row in df.iterrows():
                        weather = row['weather']
                        if weather not in weather_consumption:
                            weather_consumption[weather] = {'water': [], 'food': []}
                        weather_consumption[weather]['water'].append(row['water_consumed'])
                        weather_consumption[weather]['food'].append(row['food_consumed'])

        if weather_consumption:
            weather_types = list(weather_consumption.keys())
            water_means = [np.mean(weather_consumption[w]['water']) for w in weather_types]
            food_means = [np.mean(weather_consumption[w]['food']) for w in weather_types]

            x = np.arange(len(weather_types))
            width = 0.35

            ax3.bar(x - width/2, water_means, width, label='水', alpha=0.7, color='lightblue')
            ax3.bar(x + width/2, food_means, width, label='食物', alpha=0.7, color='lightcoral')

            ax3.set_title('不同天气下的平均消耗', fontweight='bold')
            ax3.set_xlabel('天气类型')
            ax3.set_ylabel('平均消耗 (箱)')
            ax3.set_xticks(x)
            ax3.set_xticklabels(weather_types)
            ax3.legend()
            ax3.grid(True, alpha=0.3)

        # 4. 累积收入分析
        ax4 = axes[1, 1]
        for level in selected_levels:
            if level in self.daily_logs:
                df = self.daily_logs[level]
                if 'income' in df.columns:
                    cumulative_income = df['income'].cumsum()
                    ax4.plot(df['day'], cumulative_income,
                            marker='d', label=f'关卡{level}', linewidth=2)

        ax4.set_title('累积收入变化', fontweight='bold')
        ax4.set_xlabel('天数')
        ax4.set_ylabel('累积收入 (元)')
        ax4.legend()
        ax4.grid(True, alpha=0.3)

        plt.tight_layout()
        plt.savefig(self.output_dir / 'resource_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()

        print("✓ 资源消耗分析图表已生成")

    def create_strategy_analysis_charts(self):
        """创建策略分析图表"""
        print("生成策略分析图表...")

        fig, axes = plt.subplots(2, 2, figsize=(15, 12))
        fig.suptitle('穿越沙漠问题 - 策略分析', fontsize=16, fontweight='bold')

        # 1. 策略类型vs净利润
        strategy_profits = {}
        for level, data in self.level_summaries.items():
            strategy = data.get('strategy_type', 'unknown')
            profit = data.get('net_profit', 0)
            if strategy not in strategy_profits:
                strategy_profits[strategy] = []
            strategy_profits[strategy].append(profit)

        ax1 = axes[0, 0]
        strategies = list(strategy_profits.keys())
        avg_profits = [np.mean(strategy_profits[s]) for s in strategies]
        colors = ['lightgreen', 'lightcoral', 'lightblue', 'gold']

        bars = ax1.bar(strategies, avg_profits, color=colors[:len(strategies)], alpha=0.7)
        ax1.set_title('不同策略的平均净利润', fontweight='bold')
        ax1.set_ylabel('平均净利润 (元)')
        ax1.grid(True, alpha=0.3)

        for bar, value in zip(bars, avg_profits):
            height = bar.get_height()
            ax1.text(bar.get_x() + bar.get_width()/2., height + (50 if height >= 0 else -200),
                    f'{value:,.0f}', ha='center', va='bottom' if height >= 0 else 'top')

        # 2. 关卡难度分析 (基于净利润和用时)
        ax2 = axes[0, 1]
        levels = []
        profits = []
        times = []

        for level in range(1, 7):
            if level in self.level_summaries:
                levels.append(level)
                profits.append(self.level_summaries[level].get('net_profit', 0))
                times.append(self.level_summaries[level].get('final_day', 0))

        # 气泡图：x=用时，y=净利润，气泡大小=关卡号
        scatter = ax2.scatter(times, profits, s=[l*100 for l in levels],
                             alpha=0.6, c=levels, cmap='viridis')

        for i, level in enumerate(levels):
            ax2.annotate(f'关卡{level}', (times[i], profits[i]),
                        xytext=(5, 5), textcoords='offset points')

        ax2.set_title('关卡难度分析 (气泡图)', fontweight='bold')
        ax2.set_xlabel('用时 (天)')
        ax2.set_ylabel('净利润 (元)')
        ax2.grid(True, alpha=0.3)
        ax2.axhline(y=0, color='red', linestyle='--', alpha=0.5)

        # 3. 效率vs收益散点图
        ax3 = axes[1, 0]
        efficiency = [p/t if t > 0 else 0 for p, t in zip(profits, times)]

        colors_scatter = ['green' if p > 0 else 'red' for p in profits]
        ax3.scatter(efficiency, profits, c=colors_scatter, s=100, alpha=0.7)

        for i, level in enumerate(levels):
            ax3.annotate(f'关卡{level}', (efficiency[i], profits[i]),
                        xytext=(5, 5), textcoords='offset points')

        ax3.set_title('效率vs收益分析', fontweight='bold')
        ax3.set_xlabel('效率 (元/天)')
        ax3.set_ylabel('净利润 (元)')
        ax3.grid(True, alpha=0.3)
        ax3.axhline(y=0, color='black', linestyle='--', alpha=0.5)
        ax3.axvline(x=0, color='black', linestyle='--', alpha=0.5)

        # 4. 成功率分析
        ax4 = axes[1, 1]
        success_count = sum(1 for data in self.level_summaries.values()
                           if data.get('net_profit', 0) > 0)
        total_count = len(self.level_summaries)
        failure_count = total_count - success_count

        labels = ['盈利关卡', '亏损关卡']
        sizes = [success_count, failure_count]
        colors_pie = ['lightgreen', 'lightcoral']

        wedges, texts, autotexts = ax4.pie(sizes, labels=labels, autopct='%1.1f%%',
                                          colors=colors_pie, startangle=90)
        ax4.set_title('盈利成功率', fontweight='bold')

        plt.tight_layout()
        plt.savefig(self.output_dir / 'strategy_analysis.png', dpi=300, bbox_inches='tight')
        plt.close()

        print("✓ 策略分析图表已生成")

    def create_mathematical_model_summary(self):
        """创建数学模型汇总表"""
        print("生成数学模型汇总...")

        fig, ax = plt.subplots(figsize=(16, 10))
        fig.suptitle('穿越沙漠问题 - 数学模型汇总', fontsize=16, fontweight='bold')

        # 准备表格数据
        table_data = []
        headers = ['关卡', '模型类型', '变量数', '约束数', '求解方法', '时间复杂度']

        for level in range(1, 7):
            if level in self.mathematical_models:
                model = self.mathematical_models[level]
                complexity = model.get('complexity_analysis', {})

                row = [
                    f'关卡{level}',
                    model.get('model_type', 'N/A'),
                    complexity.get('variables_count', 'N/A'),
                    complexity.get('constraints_count', 'N/A'),
                    model.get('solution_method', 'N/A'),
                    complexity.get('time_complexity', 'N/A')
                ]
                table_data.append(row)

        # 创建表格
        ax.axis('tight')
        ax.axis('off')

        table = ax.table(cellText=table_data, colLabels=headers,
                        cellLoc='center', loc='center',
                        colWidths=[0.1, 0.2, 0.1, 0.1, 0.25, 0.25])

        table.auto_set_font_size(False)
        table.set_fontsize(10)
        table.scale(1.2, 2)

        # 设置表格样式
        for i in range(len(headers)):
            table[(0, i)].set_facecolor('#4CAF50')
            table[(0, i)].set_text_props(weight='bold', color='white')

        for i in range(1, len(table_data) + 1):
            for j in range(len(headers)):
                if i % 2 == 0:
                    table[(i, j)].set_facecolor('#f0f0f0')

        plt.savefig(self.output_dir / 'mathematical_model_summary.png',
                   dpi=300, bbox_inches='tight')
        plt.close()

        print("✓ 数学模型汇总表已生成")

    def generate_summary_report(self):
        """生成汇总报告"""
        print("生成汇总报告...")

        report_content = []
        report_content.append("# 穿越沙漠数学建模项目 - 可视化分析报告\n")
        report_content.append(f"生成时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}\n\n")

        # 项目概述
        report_content.append("## 项目概述\n")
        report_content.append("本项目是一个严格按照原题规则实现的穿越沙漠数学建模问题求解器。")
        report_content.append("通过优化路径规划和策略选择来最大化收益。\n\n")

        # 关卡汇总
        report_content.append("## 关卡汇总\n")
        report_content.append("| 关卡 | 策略类型 | 最终资金 | 净利润 | 用时 | 状态 |\n")
        report_content.append("|------|----------|----------|--------|------|------|\n")

        total_profit = 0
        for level in range(1, 7):
            if level in self.level_summaries:
                data = self.level_summaries[level]
                strategy = data.get('strategy_type', 'N/A')
                final_money = data.get('final_money', 0)
                net_profit = data.get('net_profit', 0)
                days = data.get('final_day', 0)
                status = "✅盈利" if net_profit > 0 else "❌亏损"

                total_profit += net_profit

                report_content.append(f"| {level} | {strategy} | {final_money:,} | {net_profit:,} | {days} | {status} |\n")

        report_content.append(f"\n**总净利润**: {total_profit:,} 元\n")
        report_content.append(f"**平均净利润**: {total_profit/6:,.0f} 元\n\n")

        # 关键发现
        report_content.append("## 关键发现\n\n")

        # 找出最佳关卡
        best_level = max(self.level_summaries.keys(),
                        key=lambda x: self.level_summaries[x].get('net_profit', 0))
        best_profit = self.level_summaries[best_level].get('net_profit', 0)

        report_content.append(f"1. **最佳盈利关卡**: 关卡{best_level}，净利润{best_profit:,}元\n")

        # 策略分析
        strategy_counts = {}
        for data in self.level_summaries.values():
            strategy = data.get('strategy_type', 'unknown')
            strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1

        most_common_strategy = max(strategy_counts.keys(), key=lambda x: strategy_counts[x])
        report_content.append(f"2. **主要策略**: {most_common_strategy}策略，使用{strategy_counts[most_common_strategy]}次\n")

        # 成功率
        success_rate = sum(1 for data in self.level_summaries.values()
                          if data.get('net_profit', 0) > 0) / len(self.level_summaries) * 100
        report_content.append(f"3. **盈利成功率**: {success_rate:.1f}%\n")

        report_content.append("\n## 生成的图表\n\n")
        report_content.append("1. **关卡对比分析** (`level_comparison.png`): 各关卡净利润、最终资金、用时和策略分布对比\n")
        report_content.append("2. **趋势分析** (`trend_analysis.png`): 各项指标的变化趋势和效率分析\n")
        report_content.append("3. **资源消耗分析** (`resource_analysis.png`): 水、食物消耗和天气影响分析\n")
        report_content.append("4. **策略分析** (`strategy_analysis.png`): 不同策略效果和关卡难度分析\n")
        report_content.append("5. **数学模型汇总** (`mathematical_model_summary.png`): 各关卡数学模型参数汇总\n\n")

        report_content.append("## 技术说明\n\n")
        report_content.append("- **数据来源**: 项目results目录下的JSON和CSV文件\n")
        report_content.append("- **可视化工具**: matplotlib, seaborn, pandas\n")
        report_content.append("- **图表格式**: PNG格式，300 DPI高清输出\n")
        report_content.append("- **字体支持**: 支持中文显示\n\n")

        # 保存报告
        with open(self.output_dir / 'visualization_report.md', 'w', encoding='utf-8') as f:
            f.writelines(report_content)

        print("✓ 汇总报告已生成")

    def generate_all_charts(self):
        """生成所有图表"""
        print("开始生成所有可视化图表...")
        print("="*50)

        try:
            self.create_level_comparison_charts()
            self.create_trend_analysis_charts()
            self.create_resource_analysis_charts()
            self.create_strategy_analysis_charts()
            self.create_mathematical_model_summary()
            self.generate_summary_report()

            print("="*50)
            print("✅ 所有图表生成完成！")
            print(f"📁 输出目录: {self.output_dir.absolute()}")
            print("\n生成的文件:")
            for file in self.output_dir.glob('*'):
                print(f"  - {file.name}")

        except Exception as e:
            print(f"❌ 生成图表时出错: {e}")
            import traceback
            traceback.print_exc()


def main():
    """主函数"""
    print("穿越沙漠数学建模项目 - 数据可视化生成器")
    print("="*50)

    # 创建可视化器实例
    visualizer = DesertCrossingVisualizer()

    # 生成所有图表
    visualizer.generate_all_charts()


if __name__ == "__main__":
    main()
