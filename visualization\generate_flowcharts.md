# 穿越沙漠数学建模项目 - 流程图集合

## 1. 整体算法流程图

```mermaid
flowchart TD
    A[开始] --> B[读取关卡配置]
    B --> C[初始化求解器]
    C --> D[选择关卡]
    D --> E{是否为多人关卡?}
    
    E -->|是| F[多人博弈求解]
    E -->|否| G[单人优化求解]
    
    F --> H[枚举策略组合]
    G --> I[枚举挖矿天数]
    
    H --> J[计算路径和资源]
    I --> J
    
    J --> K[验证约束条件]
    K --> L{约束满足?}
    
    L -->|否| M[跳过当前策略]
    L -->|是| N[计算收益]
    
    M --> O{还有策略?}
    N --> P[更新最优解]
    P --> O
    
    O -->|是| H
    O -->|否| Q[输出最优策略]
    
    Q --> R[生成详细报告]
    R --> S[保存结果文件]
    S --> T[结束]
    
    style A fill:#e1f5fe
    style T fill:#c8e6c9
    style F fill:#fff3e0
    style G fill:#fff3e0
    style L fill:#ffecb3
```

## 2. 单关卡求解流程图

```mermaid
flowchart TD
    A[输入关卡参数] --> B[加载天气数据]
    B --> C[初始化BFS路径查找]
    C --> D[计算最短路径]
    D --> E[枚举挖矿策略]
    
    E --> F[设置挖矿天数 = 0]
    F --> G[计算直达策略]
    G --> H[验证时间和负重约束]
    
    H --> I{约束满足?}
    I -->|是| J[计算净利润]
    I -->|否| K[标记为不可行]
    
    J --> L[更新最优解]
    K --> L
    L --> M[挖矿天数 += 1]
    M --> N{挖矿天数 < 最大值?}
    
    N -->|是| O[计算挖矿策略]
    N -->|否| P[返回最优解]
    
    O --> Q[规划挖矿路径]
    Q --> R[计算资源消耗]
    R --> S[验证约束条件]
    S --> I
    
    P --> T[生成执行计划]
    T --> U[输出结果]
    
    style A fill:#e3f2fd
    style U fill:#e8f5e8
    style I fill:#fff9c4
    style N fill:#fff9c4
```

## 3. 资源计算流程图

```mermaid
flowchart TD
    A[开始资源计算] --> B[获取当前位置]
    B --> C[获取当天天气]
    C --> D[确定行动类型]
    
    D --> E{行动类型}
    E -->|移动| F[计算移动消耗]
    E -->|挖矿| G[计算挖矿消耗]
    E -->|停留| H[计算停留消耗]
    
    F --> I[基础消耗 × 2]
    G --> J[基础消耗 × 1.5]
    H --> K[基础消耗 × 1]
    
    I --> L[应用天气修正]
    J --> L
    K --> L
    
    L --> M{天气类型}
    M -->|高温| N[消耗 × 1.5]
    M -->|沙暴| O[消耗 × 2]
    M -->|晴朗| P[消耗 × 1]
    
    N --> Q[更新资源状态]
    O --> Q
    P --> Q
    
    Q --> R[检查资源充足性]
    R --> S{资源足够?}
    
    S -->|是| T[返回消耗量]
    S -->|否| U[返回不可行]
    
    style A fill:#e1f5fe
    style T fill:#c8e6c9
    style U fill:#ffcdd2
    style M fill:#fff3e0
    style S fill:#fff9c4
```

## 4. 约束检查流程图

```mermaid
flowchart TD
    A[开始约束检查] --> B[检查时间约束]
    B --> C{总用时 ≤ 时间限制?}
    
    C -->|否| D[时间约束违反]
    C -->|是| E[检查负重约束]
    
    E --> F{每日负重 ≤ 负重上限?}
    F -->|否| G[负重约束违反]
    F -->|是| H[检查资源约束]
    
    H --> I{水和食物 ≥ 0?}
    I -->|否| J[资源约束违反]
    I -->|是| K[检查沙暴约束]
    
    K --> L{沙暴日是否停留?}
    L -->|否| M[沙暴约束违反]
    L -->|是| N[检查路径约束]
    
    N --> O{路径连续且到达终点?}
    O -->|否| P[路径约束违反]
    O -->|是| Q[所有约束满足]
    
    D --> R[返回不可行]
    G --> R
    J --> R
    M --> R
    P --> R
    Q --> S[返回可行]
    
    style A fill:#e3f2fd
    style S fill:#c8e6c9
    style R fill:#ffcdd2
    style C fill:#fff9c4
    style F fill:#fff9c4
    style I fill:#fff9c4
    style L fill:#fff9c4
    style O fill:#fff9c4
```

## 5. 决策树流程图

```mermaid
flowchart TD
    A[关卡开始] --> B{天气信息已知?}
    
    B -->|是| C[使用确定性策略]
    B -->|否| D[使用随机策略]
    
    C --> E[计算最优路径]
    D --> F[基于当天天气决策]
    
    E --> G{是否访问矿山?}
    F --> G
    
    G -->|是| H[选择挖矿策略]
    G -->|否| I[选择直达策略]
    
    H --> J[计算挖矿收益]
    I --> K[计算直达收益]
    
    J --> L{收益 > 成本?}
    K --> M{时间充足?}
    
    L -->|是| N[执行挖矿计划]
    L -->|否| O[改为直达策略]
    
    M -->|是| P[执行直达计划]
    M -->|否| Q[优化路径]
    
    N --> R[每日执行检查]
    O --> R
    P --> R
    Q --> R
    
    R --> S{到达终点?}
    S -->|否| T[继续下一天]
    S -->|是| U[计算最终收益]
    
    T --> V{资源充足?}
    V -->|是| G
    V -->|否| W[紧急策略]
    
    W --> X[最短路径到终点]
    X --> U
    U --> Y[关卡结束]
    
    style A fill:#e1f5fe
    style Y fill:#c8e6c9
    style B fill:#fff3e0
    style G fill:#fff3e0
    style L fill:#fff9c4
    style M fill:#fff9c4
    style S fill:#fff9c4
    style V fill:#fff9c4
```

## 6. 数据流图

```mermaid
flowchart LR
    A[配置文件] --> B[关卡求解器]
    C[天气数据] --> B
    D[地图数据] --> B
    
    B --> E[路径规划器]
    B --> F[资源计算器]
    B --> G[约束检查器]
    
    E --> H[最优策略]
    F --> H
    G --> H
    
    H --> I[结果导出器]
    
    I --> J[JSON汇总]
    I --> K[CSV日志]
    I --> L[文本报告]
    I --> M[数学模型]
    
    J --> N[可视化系统]
    K --> N
    L --> N
    M --> N
    
    N --> O[统计图表]
    N --> P[趋势分析]
    N --> Q[对比报告]
    
    style B fill:#e3f2fd
    style H fill:#fff3e0
    style N fill:#f3e5f5
    style O fill:#e8f5e8
    style P fill:#e8f5e8
    style Q fill:#e8f5e8
```

## 使用说明

这些流程图使用Mermaid语法编写，可以在支持Mermaid的环境中渲染：

1. **GitHub**: 直接在README.md中显示
2. **VS Code**: 安装Mermaid Preview插件
3. **在线工具**: https://mermaid.live/
4. **文档系统**: GitBook, Notion等

每个流程图展示了算法的不同方面：
- 整体流程：展示完整的求解过程
- 单关卡求解：详细的单个关卡处理逻辑
- 资源计算：资源消耗的计算方法
- 约束检查：各种约束条件的验证过程
- 决策树：实时决策的逻辑结构
- 数据流：系统各组件间的数据流动
