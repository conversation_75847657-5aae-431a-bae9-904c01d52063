{"level": 1, "problem_type": "确定性MINLP", "variables": {"spatial": "x_ij^t, s_i^t, l_i^t for i,j ∈ {1,...,27}", "resource": "w_t, f_t, w_0, f_0", "action": "m_i^t, b_i^t", "state": "M_t, arrived_t"}, "parameters": {"nodes": 27, "time_limit": 30, "initial_money": 10000, "players": 1, "weather_known": true}, "objective": "max Z = M_T + 0.5·P_w·w_T + 0.5·P_f·f_T", "solution_method": "动态规划", "complexity": "O(N²·T·W)"}