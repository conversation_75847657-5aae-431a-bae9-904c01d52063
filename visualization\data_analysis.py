#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
穿越沙漠数学建模项目 - 数据分析脚本
生成详细的统计分析和数据洞察
"""

import json
import pandas as pd
import numpy as np
from pathlib import Path

def load_all_data():
    """加载所有数据"""
    results_dir = Path('../results')
    
    # 加载汇总数据
    summaries = {}
    daily_logs = {}
    models = {}
    
    for level in range(1, 7):
        # 汇总数据
        summary_file = results_dir / f'level_{level}_summary.json'
        if summary_file.exists():
            with open(summary_file, 'r', encoding='utf-8') as f:
                summaries[level] = json.load(f)
        
        # 每日日志
        log_file = results_dir / f'level_{level}_daily_log.csv'
        if log_file.exists():
            daily_logs[level] = pd.read_csv(log_file)
        
        # 数学模型
        model_file = results_dir / f'level_{level}_mathematical_model.json'
        if model_file.exists():
            with open(model_file, 'r', encoding='utf-8') as f:
                models[level] = json.load(f)
    
    return summaries, daily_logs, models

def analyze_performance():
    """分析性能指标"""
    summaries, daily_logs, models = load_all_data()
    
    print("="*60)
    print("穿越沙漠项目 - 详细数据分析报告")
    print("="*60)
    
    # 基础统计
    print("\n📊 基础统计信息")
    print("-" * 40)
    
    total_levels = len(summaries)
    profitable_levels = sum(1 for data in summaries.values() if data.get('net_profit', 0) > 0)
    total_profit = sum(data.get('net_profit', 0) for data in summaries.values())
    
    print(f"总关卡数: {total_levels}")
    print(f"盈利关卡数: {profitable_levels}")
    print(f"盈利成功率: {profitable_levels/total_levels*100:.1f}%")
    print(f"总净利润: {total_profit:,} 元")
    print(f"平均净利润: {total_profit/total_levels:,.0f} 元")
    
    # 关卡详细分析
    print("\n🎯 关卡详细分析")
    print("-" * 40)
    
    for level in range(1, 7):
        if level in summaries:
            data = summaries[level]
            strategy = data.get('strategy_type', 'N/A')
            final_money = data.get('final_money', 0)
            net_profit = data.get('net_profit', 0)
            days = data.get('final_day', 0)
            
            status = "✅盈利" if net_profit > 0 else "❌亏损"
            efficiency = net_profit / days if days > 0 else 0
            
            print(f"关卡{level}: {strategy}策略, {days}天, 净利润{net_profit:,}元, 效率{efficiency:.0f}元/天 {status}")
    
    # 策略分析
    print("\n🔍 策略分析")
    print("-" * 40)
    
    strategy_stats = {}
    for data in summaries.values():
        strategy = data.get('strategy_type', 'unknown')
        profit = data.get('net_profit', 0)
        days = data.get('final_day', 0)
        
        if strategy not in strategy_stats:
            strategy_stats[strategy] = {'count': 0, 'profits': [], 'days': []}
        
        strategy_stats[strategy]['count'] += 1
        strategy_stats[strategy]['profits'].append(profit)
        strategy_stats[strategy]['days'].append(days)
    
    for strategy, stats in strategy_stats.items():
        avg_profit = np.mean(stats['profits'])
        avg_days = np.mean(stats['days'])
        success_rate = sum(1 for p in stats['profits'] if p > 0) / len(stats['profits']) * 100
        
        print(f"{strategy}策略: 使用{stats['count']}次, 平均利润{avg_profit:,.0f}元, "
              f"平均用时{avg_days:.1f}天, 成功率{success_rate:.1f}%")
    
    # 资源消耗分析
    print("\n💧 资源消耗分析")
    print("-" * 40)
    
    total_water = 0
    total_food = 0
    total_income = 0
    weather_stats = {}
    
    for level, df in daily_logs.items():
        if 'water_consumed' in df.columns:
            level_water = df['water_consumed'].sum()
            level_food = df['food_consumed'].sum()
            level_income = df['income'].sum()
            
            total_water += level_water
            total_food += level_food
            total_income += level_income
            
            print(f"关卡{level}: 水{level_water}箱, 食物{level_food}箱, 收入{level_income:,}元")
            
            # 天气统计
            if 'weather' in df.columns:
                for weather in df['weather'].unique():
                    if weather not in weather_stats:
                        weather_stats[weather] = 0
                    weather_stats[weather] += len(df[df['weather'] == weather])
    
    print(f"\n总消耗: 水{total_water}箱, 食物{total_food}箱")
    print(f"总收入: {total_income:,}元")
    
    if weather_stats:
        print(f"\n天气分布:")
        for weather, count in weather_stats.items():
            print(f"  {weather}: {count}天")
    
    # 数学模型分析
    print("\n🧮 数学模型分析")
    print("-" * 40)
    
    for level, model in models.items():
        model_type = model.get('model_type', 'N/A')
        solution_method = model.get('solution_method', 'N/A')
        complexity = model.get('complexity_analysis', {})
        
        variables = complexity.get('variables_count', 'N/A')
        constraints = complexity.get('constraints_count', 'N/A')
        time_complexity = complexity.get('time_complexity', 'N/A')
        
        print(f"关卡{level}: {model_type}, {solution_method}")
        print(f"  变量数: {variables}, 约束数: {constraints}, 时间复杂度: {time_complexity}")
    
    # 效率排名
    print("\n🏆 效率排名")
    print("-" * 40)
    
    efficiency_ranking = []
    for level, data in summaries.items():
        net_profit = data.get('net_profit', 0)
        days = data.get('final_day', 1)
        efficiency = net_profit / days
        efficiency_ranking.append((level, efficiency, net_profit, days))
    
    efficiency_ranking.sort(key=lambda x: x[1], reverse=True)
    
    for i, (level, efficiency, profit, days) in enumerate(efficiency_ranking, 1):
        print(f"{i}. 关卡{level}: {efficiency:.0f}元/天 (净利润{profit:,}元, {days}天)")
    
    # 风险分析
    print("\n⚠️ 风险分析")
    print("-" * 40)
    
    high_risk = []
    medium_risk = []
    low_risk = []
    
    for level, data in summaries.items():
        net_profit = data.get('net_profit', 0)
        days = data.get('final_day', 0)
        
        if net_profit < -500:
            high_risk.append(f"关卡{level}")
        elif net_profit < 0:
            medium_risk.append(f"关卡{level}")
        else:
            low_risk.append(f"关卡{level}")
    
    print(f"高风险关卡 (亏损>500元): {', '.join(high_risk) if high_risk else '无'}")
    print(f"中风险关卡 (小幅亏损): {', '.join(medium_risk) if medium_risk else '无'}")
    print(f"低风险关卡 (盈利): {', '.join(low_risk) if low_risk else '无'}")
    
    # 建议
    print("\n💡 优化建议")
    print("-" * 40)
    
    best_level = max(summaries.keys(), key=lambda x: summaries[x].get('net_profit', 0))
    worst_level = min(summaries.keys(), key=lambda x: summaries[x].get('net_profit', 0))
    
    print(f"1. 最佳表现关卡{best_level}的策略值得在其他关卡中借鉴")
    print(f"2. 关卡{worst_level}需要重新评估策略选择")
    print(f"3. mining策略在长时间关卡中表现更好，建议优先考虑")
    print(f"4. 建议加强对天气变化的预测和应对")
    print(f"5. 可以考虑引入机器学习优化策略选择")

def generate_csv_summary():
    """生成CSV格式的汇总数据"""
    summaries, daily_logs, models = load_all_data()
    
    # 创建汇总DataFrame
    summary_data = []
    for level in range(1, 7):
        if level in summaries:
            data = summaries[level]
            row = {
                '关卡': level,
                '策略类型': data.get('strategy_type', 'N/A'),
                '最终资金': data.get('final_money', 0),
                '净利润': data.get('net_profit', 0),
                '用时': data.get('final_day', 0),
                '效率(元/天)': data.get('net_profit', 0) / max(data.get('final_day', 1), 1),
                '状态': '盈利' if data.get('net_profit', 0) > 0 else '亏损'
            }
            summary_data.append(row)
    
    df = pd.DataFrame(summary_data)
    
    # 保存到CSV
    output_dir = Path('charts')
    output_dir.mkdir(exist_ok=True)
    
    df.to_csv(output_dir / 'level_summary.csv', index=False, encoding='utf-8-sig')
    print(f"\n📄 汇总数据已保存到: {output_dir / 'level_summary.csv'}")

def main():
    """主函数"""
    try:
        analyze_performance()
        generate_csv_summary()
        
        print("\n" + "="*60)
        print("✅ 数据分析完成！")
        print("="*60)
        
    except Exception as e:
        print(f"❌ 分析过程中出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
