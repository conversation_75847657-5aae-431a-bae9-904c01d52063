#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正版图表生成器 - 确保数据准确性
"""

import json
import matplotlib
matplotlib.use('Agg')  # 使用非交互式后端
import matplotlib.pyplot as plt
import numpy as np
from pathlib import Path

# 设置中文字体
plt.rcParams['font.sans-serif'] = ['SimHei', 'Microsoft YaHei', 'DejaVu Sans']
plt.rcParams['axes.unicode_minus'] = False

def load_verified_data():
    """加载并验证数据"""
    print("正在加载数据...")
    
    # 手动输入验证过的准确数据（基于JSON文件验证）
    verified_data = {
        1: {"strategy": "direct", "final_money": 9410, "net_profit": -590, "days": 3},
        2: {"strategy": "mining", "final_money": 20085, "net_profit": 10085, "days": 22},
        3: {"strategy": "mining", "final_money": 8915, "net_profit": -1085, "days": 7},
        4: {"strategy": "mining", "final_money": 22550, "net_profit": 12550, "days": 23},
        5: {"strategy": "direct", "final_money": 9020, "net_profit": -980, "days": 3},
        6: {"strategy": "mining", "final_money": 22550, "net_profit": 12550, "days": 23}
    }
    
    print("数据验证:")
    for level, data in verified_data.items():
        print(f"关卡{level}: {data['strategy']}, 净利润{data['net_profit']}, 最终资金{data['final_money']}, 用时{data['days']}天")
    
    return verified_data

def create_corrected_comparison_chart():
    """创建修正后的对比图表"""
    print("生成修正后的对比图表...")
    
    # 创建输出目录
    output_dir = Path('charts')
    output_dir.mkdir(exist_ok=True)
    
    # 加载验证数据
    data = load_verified_data()
    
    # 准备数据
    levels = [f'关卡{i}' for i in range(1, 7)]
    net_profits = [data[i]['net_profit'] for i in range(1, 7)]
    final_money = [data[i]['final_money'] for i in range(1, 7)]
    days_used = [data[i]['days'] for i in range(1, 7)]
    strategies = [data[i]['strategy'] for i in range(1, 7)]
    
    print(f"净利润数据: {net_profits}")
    print(f"最终资金数据: {final_money}")
    print(f"用时数据: {days_used}")
    
    # 创建图表
    fig, ((ax1, ax2), (ax3, ax4)) = plt.subplots(2, 2, figsize=(15, 12))
    fig.suptitle('穿越沙漠问题 - 修正后的关卡对比分析', fontsize=16, fontweight='bold')
    
    # 1. 净利润对比
    colors = ['red' if x < 0 else 'green' for x in net_profits]
    bars1 = ax1.bar(levels, net_profits, color=colors, alpha=0.7)
    ax1.set_title('各关卡净利润对比', fontweight='bold')
    ax1.set_ylabel('净利润 (元)')
    ax1.axhline(y=0, color='black', linestyle='--', alpha=0.5)
    ax1.grid(True, alpha=0.3)
    
    # 添加数值标签
    for bar, value in zip(bars1, net_profits):
        height = bar.get_height()
        ax1.text(bar.get_x() + bar.get_width()/2., 
                height + (200 if height >= 0 else -500),
                f'{value:,}', ha='center', 
                va='bottom' if height >= 0 else 'top',
                fontweight='bold')
    
    # 2. 最终资金对比
    bars2 = ax2.bar(levels, final_money, color='steelblue', alpha=0.7)
    ax2.set_title('各关卡最终资金对比', fontweight='bold')
    ax2.set_ylabel('最终资金 (元)')
    ax2.grid(True, alpha=0.3)
    
    for bar, value in zip(bars2, final_money):
        height = bar.get_height()
        ax2.text(bar.get_x() + bar.get_width()/2., height + 300,
                f'{value:,}', ha='center', va='bottom', fontweight='bold')
    
    # 3. 用时对比
    bars3 = ax3.bar(levels, days_used, color='orange', alpha=0.7)
    ax3.set_title('各关卡用时对比', fontweight='bold')
    ax3.set_ylabel('用时 (天)')
    ax3.grid(True, alpha=0.3)
    
    for bar, value in zip(bars3, days_used):
        height = bar.get_height()
        ax3.text(bar.get_x() + bar.get_width()/2., height + 0.5,
                f'{value}', ha='center', va='bottom', fontweight='bold')
    
    # 4. 策略类型分布
    strategy_counts = {}
    for strategy in strategies:
        strategy_counts[strategy] = strategy_counts.get(strategy, 0) + 1
    
    colors_pie = ['lightcoral', 'lightblue']
    wedges, texts, autotexts = ax4.pie(strategy_counts.values(), 
                                      labels=[f'{k}策略' for k in strategy_counts.keys()], 
                                      autopct='%1.1f%%', 
                                      colors=colors_pie,
                                      startangle=90)
    ax4.set_title('策略类型分布', fontweight='bold')
    
    # 设置字体
    for autotext in autotexts:
        autotext.set_fontweight('bold')
    
    plt.tight_layout()
    plt.savefig(output_dir / 'corrected_comparison.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ 修正后的对比图表已保存: {output_dir / 'corrected_comparison.png'}")

def create_corrected_trend_chart():
    """创建修正后的趋势图表"""
    print("生成修正后的趋势图表...")
    
    output_dir = Path('charts')
    data = load_verified_data()
    
    # 准备数据
    levels_num = list(range(1, 7))
    net_profits = [data[i]['net_profit'] for i in range(1, 7)]
    final_money = [data[i]['final_money'] for i in range(1, 7)]
    days_used = [data[i]['days'] for i in range(1, 7)]
    
    # 计算效率和ROI
    efficiency = [p/d if d > 0 else 0 for p, d in zip(net_profits, days_used)]
    roi = [(f-10000)/10000*100 for f in final_money]
    
    print(f"效率数据: {efficiency}")
    print(f"ROI数据: {roi}")
    
    # 创建图表
    fig, axes = plt.subplots(2, 3, figsize=(18, 12))
    fig.suptitle('穿越沙漠问题 - 修正后的趋势分析', fontsize=16, fontweight='bold')
    
    # 1. 净利润趋势线
    axes[0,0].plot(levels_num, net_profits, marker='o', linewidth=3, markersize=10, color='green')
    axes[0,0].fill_between(levels_num, net_profits, alpha=0.3, color='green')
    axes[0,0].set_title('净利润变化趋势', fontweight='bold')
    axes[0,0].set_xlabel('关卡')
    axes[0,0].set_ylabel('净利润 (元)')
    axes[0,0].grid(True, alpha=0.3)
    axes[0,0].axhline(y=0, color='red', linestyle='--', alpha=0.7)
    axes[0,0].set_xticks(levels_num)
    
    # 添加数值标签
    for i, v in enumerate(net_profits):
        axes[0,0].text(levels_num[i], v + (500 if v >= 0 else -800), f'{v:,}', 
                      ha='center', va='bottom' if v >= 0 else 'top', fontweight='bold')
    
    # 2. 最终资金趋势线
    axes[0,1].plot(levels_num, final_money, marker='s', linewidth=3, markersize=10, color='blue')
    axes[0,1].fill_between(levels_num, final_money, alpha=0.3, color='blue')
    axes[0,1].set_title('最终资金变化趋势', fontweight='bold')
    axes[0,1].set_xlabel('关卡')
    axes[0,1].set_ylabel('最终资金 (元)')
    axes[0,1].grid(True, alpha=0.3)
    axes[0,1].set_xticks(levels_num)
    
    # 3. 用时趋势线
    axes[0,2].plot(levels_num, days_used, marker='^', linewidth=3, markersize=10, color='orange')
    axes[0,2].fill_between(levels_num, days_used, alpha=0.3, color='orange')
    axes[0,2].set_title('用时变化趋势', fontweight='bold')
    axes[0,2].set_xlabel('关卡')
    axes[0,2].set_ylabel('用时 (天)')
    axes[0,2].grid(True, alpha=0.3)
    axes[0,2].set_xticks(levels_num)
    
    # 4. 效率分析
    colors_eff = ['red' if x < 0 else 'green' for x in efficiency]
    bars4 = axes[1,0].bar(levels_num, efficiency, color=colors_eff, alpha=0.7)
    axes[1,0].set_title('日均收益效率', fontweight='bold')
    axes[1,0].set_xlabel('关卡')
    axes[1,0].set_ylabel('日均净利润 (元/天)')
    axes[1,0].grid(True, alpha=0.3)
    axes[1,0].axhline(y=0, color='black', linestyle='--', alpha=0.5)
    axes[1,0].set_xticks(levels_num)
    
    for bar, value in zip(bars4, efficiency):
        height = bar.get_height()
        axes[1,0].text(bar.get_x() + bar.get_width()/2., 
                      height + (10 if height >= 0 else -30),
                      f'{value:.0f}', ha='center', 
                      va='bottom' if height >= 0 else 'top', fontweight='bold')
    
    # 5. 投资回报率
    colors_roi = ['red' if x < 0 else 'green' for x in roi]
    bars5 = axes[1,1].bar(levels_num, roi, color=colors_roi, alpha=0.7)
    axes[1,1].set_title('投资回报率', fontweight='bold')
    axes[1,1].set_xlabel('关卡')
    axes[1,1].set_ylabel('ROI (%)')
    axes[1,1].axhline(y=0, color='black', linestyle='--', alpha=0.5)
    axes[1,1].grid(True, alpha=0.3)
    axes[1,1].set_xticks(levels_num)
    
    for bar, value in zip(bars5, roi):
        height = bar.get_height()
        axes[1,1].text(bar.get_x() + bar.get_width()/2., 
                      height + (0.5 if height >= 0 else -1.5),
                      f'{value:.1f}%', ha='center', 
                      va='bottom' if height >= 0 else 'top', fontweight='bold')
    
    # 6. 综合评分（基于标准化的利润和效率）
    norm_profit = [(p - min(net_profits))/(max(net_profits) - min(net_profits)) * 100 
                   if max(net_profits) != min(net_profits) else 50 for p in net_profits]
    
    bars6 = axes[1,2].bar(levels_num, norm_profit, color='gold', alpha=0.7)
    axes[1,2].set_title('综合评分 (基于利润标准化)', fontweight='bold')
    axes[1,2].set_xlabel('关卡')
    axes[1,2].set_ylabel('综合评分')
    axes[1,2].grid(True, alpha=0.3)
    axes[1,2].set_xticks(levels_num)
    
    for bar, value in zip(bars6, norm_profit):
        height = bar.get_height()
        axes[1,2].text(bar.get_x() + bar.get_width()/2., height + 2,
                      f'{value:.0f}', ha='center', va='bottom', fontweight='bold')
    
    plt.tight_layout()
    plt.savefig(output_dir / 'corrected_trend_analysis.png', dpi=300, bbox_inches='tight')
    plt.close()
    
    print(f"✅ 修正后的趋势图表已保存: {output_dir / 'corrected_trend_analysis.png'}")

def main():
    """主函数"""
    print("穿越沙漠项目 - 修正版图表生成器")
    print("="*50)
    
    try:
        create_corrected_comparison_chart()
        create_corrected_trend_chart()
        
        print("="*50)
        print("✅ 修正后的图表生成完成！")
        print("📁 输出目录: charts/")
        print("\n生成的文件:")
        print("  - corrected_comparison.png (修正后的对比图)")
        print("  - corrected_trend_analysis.png (修正后的趋势图)")
        
    except Exception as e:
        print(f"❌ 生成图表时出错: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
