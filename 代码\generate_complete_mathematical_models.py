#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
完整数学模型生成器
生成所有关卡的详细数学模型，包含完整的变量定义、约束条件、目标函数等
"""

import json
from datetime import datetime
from typing import Dict, List, Any
from config.game_config import GameConfig


class CompleteMathematicalModelGenerator:
    """完整数学模型生成器"""
    
    def __init__(self):
        self.config = GameConfig()
        
    def generate_all_models(self) -> str:
        """生成所有关卡的完整数学模型"""
        report_lines = []
        
        # 添加报告头部
        report_lines.extend([
            "=" * 120,
            "穿越沙漠游戏 - 完整数学建模报告".center(120),
            "=" * 120,
            f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"内容: 所有6个关卡的完整数学模型定义",
            "=" * 120,
            "",
            "📋 数学建模说明:",
            "   本报告包含每个关卡的完整数学模型，包括：",
            "   • 决策变量定义",
            "   • 参数设置",
            "   • 约束条件",
            "   • 目标函数",
            "   • 求解方法",
            "   • 复杂度分析",
            "",
            ""
        ])
        
        # 生成每个关卡的数学模型
        for level in range(1, 7):
            level_config = self.config.get_level_config(level)
            if level_config:
                model = self._generate_complete_model(level, level_config)
                report_lines.extend(model)
                report_lines.append("")
        
        # 添加总结
        report_lines.extend(self._generate_model_summary())
        
        return "\n".join(report_lines)
    
    def _generate_complete_model(self, level: int, level_config) -> List[str]:
        """生成单个关卡的完整数学模型"""
        lines = []
        
        # 关卡标题
        lines.extend([
            "=" * 100,
            f"第{level}关完整数学模型".center(100),
            "=" * 100,
            ""
        ])
        
        # 问题描述
        lines.extend(self._generate_problem_description(level, level_config))
        
        # 决策变量
        lines.extend(self._generate_decision_variables(level, level_config))
        
        # 参数定义
        lines.extend(self._generate_parameters(level, level_config))
        
        # 约束条件
        lines.extend(self._generate_constraints(level, level_config))
        
        # 目标函数
        lines.extend(self._generate_objective_function(level, level_config))
        
        # 求解方法
        lines.extend(self._generate_solution_method(level, level_config))
        
        # 复杂度分析
        lines.extend(self._generate_complexity_analysis(level, level_config))
        
        return lines
    
    def _generate_problem_description(self, level: int, level_config) -> List[str]:
        """生成问题描述"""
        lines = []
        
        # 关卡特点描述
        level_descriptions = {
            1: "单人基础关卡，天气完全已知，直接路径优化",
            2: "单人复杂关卡，多矿山多村庄，长期挖矿策略",
            3: "单人挑战关卡，天气未知，时间限制严格",
            4: "单人策略关卡，5x5地图，天气未知但时间充裕",
            5: "双人博弈关卡，天气已知，竞争环境下的策略选择",
            6: "三人博弈关卡，天气未知，多人竞争复杂环境"
        }
        
        lines.extend([
            f"📋 问题描述:",
            f"   关卡特点: {level_descriptions.get(level, '未知关卡')}",
            f"   玩家数量: {level_config.players}人",
            f"   地图规模: {len(level_config.map_nodes)}个节点",
            f"   时间限制: {level_config.deadline_days}天",
            f"   天气信息: {'完全已知' if level_config.weather_known else '仅知当天'}",
            f"   矿山数量: {len(level_config.mine_nodes)}个",
            f"   村庄数量: {len(level_config.village_nodes)}个",
            ""
        ])
        
        return lines
    
    def _generate_decision_variables(self, level: int, level_config) -> List[str]:
        """生成决策变量定义"""
        lines = []
        
        lines.extend([
            f"🔢 决策变量定义:",
            f"   空间变量:",
            f"     x_ij^t ∈ {{0,1}}  : 第t天从节点i移动到节点j (i,j ∈ {{1,2,...,{len(level_config.map_nodes)}}})",
            f"     s_i^t ∈ {{0,1}}   : 第t天在节点i停留",
            f"     l_i^t ∈ {{0,1}}   : 第t天玩家位于节点i",
            "",
            f"   资源变量:",
            f"     w_t ∈ ℕ         : 第t天结束时的水量 (箱)",
            f"     f_t ∈ ℕ         : 第t天结束时的食物量 (箱)",
            f"     w_0, f_0 ∈ ℕ    : 初始购买的水量和食物量",
            "",
            f"   行动变量:",
            f"     m_i^t ∈ {{0,1}}   : 第t天在矿山i挖矿",
            f"     b_i^t ∈ ℕ       : 第t天在村庄i购买的资源量",
            "",
            f"   状态变量:",
            f"     M_t ∈ ℝ+        : 第t天结束时的资金",
            f"     arrived_t ∈ {{0,1}} : 第t天是否到达终点",
            ""
        ])
        
        return lines

    def _generate_parameters(self, level: int, level_config) -> List[str]:
        """生成参数定义"""
        lines = []

        # 基础参数
        lines.extend([
            f"📊 参数定义:",
            f"   地图参数:",
            f"     N = {len(level_config.map_nodes)}           : 节点总数",
            f"     T = {level_config.deadline_days}            : 时间限制 (天)",
            f"     S = {level_config.start_node}             : 起点节点",
            f"     E = {level_config.end_node}             : 终点节点",
            f"     M = {level_config.mine_nodes}        : 矿山节点集合",
            f"     V = {level_config.village_nodes}      : 村庄节点集合",
            "",
            f"   经济参数:",
            f"     M_0 = {level_config.initial_money}        : 初始资金 (元)",
            f"     P_w = {level_config.water_price}             : 水价格 (元/箱)",
            f"     P_f = {level_config.food_price}            : 食物价格 (元/箱)",
            f"     R = {level_config.base_income}          : 挖矿基础收益 (元/天)",
            "",
            f"   物理参数:",
            f"     W_max = {level_config.max_weight}       : 负重上限 (kg)",
            f"     w_weight = {level_config.water_weight}         : 水重量 (kg/箱)",
            f"     f_weight = {level_config.food_weight}         : 食物重量 (kg/箱)",
            ""
        ])

        # 消耗参数
        lines.extend([
            f"   消耗参数:",
        ])

        for weather, consumption in level_config.consumption.items():
            lines.append(f"     {weather}天气: 水{consumption['water']}箱/天, 食物{consumption['food']}箱/天")

        lines.extend([
            f"     α = {level_config.move_factor}           : 移动消耗倍数",
            f"     β = {getattr(level_config, 'mine_consumption_multiplier', 1.5)}           : 挖矿消耗倍数",
            ""
        ])

        # 天气参数
        if level_config.weather_known:
            lines.extend([
                f"   天气参数 (已知):",
                f"     weather_t ∈ {{'晴朗','高温','沙暴'}} : 第t天天气状况",
                f"     天气预报: {len(level_config.weather_forecast)}天完整预报",
            ])
        else:
            lines.extend([
                f"   天气参数 (未知):",
                f"     weather_t ∈ {{'晴朗','高温','沙暴'}} : 第t天天气状况 (仅知当天)",
                f"     概率分布: 需要基于历史数据估算",
            ])

        lines.append("")

        return lines

    def _generate_constraints(self, level: int, level_config) -> List[str]:
        """生成约束条件"""
        lines = []

        lines.extend([
            f"⚖️ 约束条件:",
            f"   1. 初始条件约束:",
            f"      l_{level_config.start_node}^0 = 1                    (初始位置在起点)",
            f"      l_i^0 = 0, ∀i ≠ {level_config.start_node}           (其他位置为0)",
            f"      M_0 = {level_config.initial_money} - P_w·w_0 - P_f·f_0    (初始资金扣除购买成本)",
            "",
            f"   2. 位置唯一性约束:",
            f"      Σ_i l_i^t = 1, ∀t ∈ {{0,1,...,T}}      (每天只能在一个位置)",
            "",
            f"   3. 移动合法性约束:",
            f"      x_ij^t = 1 ⟹ (i,j) ∈ E_adj           (只能在相邻节点间移动)",
            f"      Σ_j x_ij^t + s_i^t = l_i^{{t-1}}, ∀i,t  (位置连续性)",
            f"      Σ_i x_ij^t = l_j^t, ∀j,t              (到达位置确定性)",
            "",
            f"   4. 沙暴日约束:",
            f"      weather_t = '沙暴' ⟹ x_ij^t = 0, ∀i,j  (沙暴日必须停留)",
            "",
            f"   5. 资源平衡约束:",
            f"      w_t = w_{{t-1}} - C_w^t + B_w^t, ∀t    (水量平衡)",
            f"      f_t = f_{{t-1}} - C_f^t + B_f^t, ∀t    (食物平衡)",
            f"      其中:",
            f"        C_w^t = Σ_i c_w(weather_t)·(α·Σ_j x_ij^t + β·m_i^t + s_i^t)·l_i^{{t-1}}",
            f"        C_f^t = Σ_i c_f(weather_t)·(α·Σ_j x_ij^t + β·m_i^t + s_i^t)·l_i^{{t-1}}",
            f"        B_w^t = Σ_{{i∈V}} b_w^{{i,t}}         (村庄购买的水)",
            f"        B_f^t = Σ_{{i∈V}} b_f^{{i,t}}         (村庄购买的食物)",
            "",
            f"   6. 负重约束:",
            f"      w_t·w_weight + f_t·f_weight ≤ W_max, ∀t  (每天负重不超限)",
            "",
            f"   7. 资源非负约束:",
            f"      w_t ≥ 0, f_t ≥ 0, ∀t                  (资源量非负)",
            "",
            f"   8. 挖矿约束:",
            f"      m_i^t = 1 ⟹ i ∈ M ∧ l_i^t = 1        (只能在矿山挖矿)",
            f"      到达矿山当天不能挖矿",
            "",
            f"   9. 村庄购买约束:",
            f"      b_i^t > 0 ⟹ i ∈ V ∧ l_i^t = 1        (只能在村庄购买)",
            f"      M_t ≥ 2·P_w·b_w^{{i,t}} + 2·P_f·b_f^{{i,t}}  (资金充足)",
            "",
            f"   10. 终点约束:",
            f"       arrived_t = 1 ⟹ l_{level_config.end_node}^t = 1     (到达终点)",
            f"       arrived_t = 1 ⟹ arrived_{{t'}} = 1, ∀t' ≥ t  (到达后保持)",
            "",
            f"   11. 时间约束:",
            f"       arrived_T = 1                         (必须在截止日期前到达)",
            ""
        ])

        return lines

    def _generate_objective_function(self, level: int, level_config) -> List[str]:
        """生成目标函数"""
        lines = []

        lines.extend([
            f"🎯 目标函数:",
            f"   最大化最终资产价值:",
            "",
            f"   max Z = M_T + 0.5·P_w·w_T + 0.5·P_f·f_T",
            "",
            f"   其中:",
            f"     M_T = M_0 - P_w·w_0 - P_f·f_0 + Σ_{{t=1}}^T Σ_{{i∈M}} R·m_i^t - Σ_{{t=1}}^T Σ_{{i∈V}} (2·P_w·b_w^{{i,t}} + 2·P_f·b_f^{{i,t}})",
            "",
            f"   目标函数组成部分:",
            f"     • M_T: 最终现金",
            f"     • 0.5·P_w·w_T: 剩余水的退回价值 (基准价格的50%)",
            f"     • 0.5·P_f·f_T: 剩余食物的退回价值 (基准价格的50%)",
            "",
            f"   收入来源:",
            f"     • 挖矿收入: Σ_{{t=1}}^T Σ_{{i∈M}} R·m_i^t",
            "",
            f"   支出项目:",
            f"     • 初始购买: P_w·w_0 + P_f·f_0",
            f"     • 村庄购买: Σ_{{t=1}}^T Σ_{{i∈V}} (2·P_w·b_w^{{i,t}} + 2·P_f·b_f^{{i,t}})",
            ""
        ])

        return lines

    def _generate_solution_method(self, level: int, level_config) -> List[str]:
        """生成求解方法"""
        lines = []

        if level_config.weather_known:
            method = "动态规划 (Dynamic Programming)"
            complexity = "O(N²·T·W_max)"
            description = "天气完全已知，可以使用确定性动态规划"
        else:
            method = "贪心策略 + 蒙特卡洛模拟"
            complexity = "O(N·T·K) where K是策略数量"
            description = "天气未知，使用保守贪心策略"

        lines.extend([
            f"🔧 求解方法:",
            f"   主要方法: {method}",
            f"   时间复杂度: {complexity}",
            f"   方法描述: {description}",
            "",
            f"   具体步骤:",
        ])

        if level_config.weather_known:
            lines.extend([
                f"     1. 状态定义: dp[t][i][w][f] = 在第t天位于节点i，拥有w箱水、f箱食物时的最大资产",
                f"     2. 状态转移: 考虑所有可能的行动(移动/停留/挖矿/购买)",
                f"     3. 边界条件: dp[0][S][w_0][f_0] = M_0",
                f"     4. 目标状态: max{{dp[t][E][w][f] | t ≤ T, w ≥ 0, f ≥ 0}}",
            ])
        else:
            lines.extend([
                f"     1. 生成多种策略候选 (直接路径、挖矿路径、村庄路径)",
                f"     2. 保守资源估算 (基于最坏天气情况)",
                f"     3. 策略评估和比较",
                f"     4. 选择预期收益最高的策略",
            ])

        lines.extend([
            "",
            f"   模型类型: {'确定性MINLP' if level_config.weather_known else '随机性MINLP'}",
            f"   变量类型: 混合整数变量 (连续变量 + 0-1变量)",
            ""
        ])

        return lines

    def _generate_complexity_analysis(self, level: int, level_config) -> List[str]:
        """生成复杂度分析"""
        lines = []

        # 计算问题规模
        nodes = len(level_config.map_nodes)
        time_horizon = level_config.deadline_days
        max_weight = level_config.max_weight

        lines.extend([
            f"📈 复杂度分析:",
            f"   问题规模:",
            f"     • 节点数: N = {nodes}",
            f"     • 时间跨度: T = {time_horizon}",
            f"     • 最大负重: W = {max_weight}",
            f"     • 玩家数: P = {level_config.players}",
            "",
            f"   变量数量估算:",
            f"     • 位置变量: N·T = {nodes * time_horizon}",
            f"     • 移动变量: N²·T = {nodes * nodes * time_horizon}",
            f"     • 资源变量: 2·T = {2 * time_horizon}",
            f"     • 行动变量: 约 {len(level_config.mine_nodes) * time_horizon + len(level_config.village_nodes) * time_horizon}",
            f"     • 总变量数: 约 {nodes * nodes * time_horizon + 3 * time_horizon}",
            "",
            f"   约束数量估算:",
            f"     • 位置约束: N·T = {nodes * time_horizon}",
            f"     • 资源约束: 4·T = {4 * time_horizon}",
            f"     • 逻辑约束: 约 {2 * time_horizon}",
            f"     • 总约束数: 约 {(nodes + 6) * time_horizon}",
            "",
            f"   计算复杂度:",
        ])

        if level_config.weather_known:
            lines.extend([
                f"     • 状态空间: O(T·N·W²) ≈ O({time_horizon}·{nodes}·{max_weight//10}²)",
                f"     • 时间复杂度: O(T·N³·W²)",
                f"     • 空间复杂度: O(T·N·W²)",
                f"     • 问题类别: NP-Hard (但可用DP在伪多项式时间内求解)",
            ])
        else:
            lines.extend([
                f"     • 策略数量: O(N!) (路径枚举)",
                f"     • 评估复杂度: O(T·N) per strategy",
                f"     • 总时间复杂度: O(K·T·N) where K是考虑的策略数",
                f"     • 问题类别: 随机规划问题",
            ])

        lines.extend([
            "",
            f"   求解难度评估: {'中等' if level_config.weather_known else '困难'}",
            f"   主要挑战: {'状态空间爆炸' if level_config.weather_known else '不确定性处理'}",
            ""
        ])

        return lines

    def _generate_model_summary(self) -> List[str]:
        """生成模型总结"""
        lines = []

        lines.extend([
            "=" * 120,
            "数学建模总结".center(120),
            "=" * 120,
            "",
            "📊 模型对比分析:",
            "",
            "┌─────────┬──────────────┬──────────────┬──────────────┬──────────────┐",
            "│  关卡   │   问题类型   │   求解方法   │   复杂度     │   主要挑战   │",
            "├─────────┼──────────────┼──────────────┼──────────────┼──────────────┤",
            "│  第1关  │  确定性MINLP │   动态规划   │    O(N²T)    │   路径优化   │",
            "│  第2关  │  确定性MINLP │   动态规划   │   O(N²TW)    │  多目标优化  │",
            "│  第3关  │  随机性MINLP │   贪心策略   │   O(KTN)     │  不确定性    │",
            "│  第4关  │  随机性MINLP │   贪心策略   │   O(KTN)     │  状态空间大  │",
            "│  第5关  │  博弈论模型  │   动态规划   │   O(N²TP)    │  多人博弈    │",
            "│  第6关  │  随机博弈    │   贪心策略   │   O(KTNP)    │ 随机+博弈    │",
            "└─────────┴──────────────┴──────────────┴──────────────┴──────────────┘",
            "",
            "🎯 关键数学概念:",
            "",
            "1. **混合整数非线性规划 (MINLP)**:",
            "   • 连续变量: 资源量、资金",
            "   • 整数变量: 位置、行动决策",
            "   • 非线性: 资源消耗与天气、行动的乘积关系",
            "",
            "2. **动态规划状态转移方程**:",
            "   dp[t][i][w][f] = max{",
            "     dp[t-1][j][w'][f'] + reward - cost",
            "     | (j,i) ∈ adjacent, resource_constraint_satisfied",
            "   }",
            "",
            "3. **随机规划模型**:",
            "   E[Z] = Σ_ω P(ω) · Z(ω)",
            "   其中 ω 表示天气场景，P(ω) 是概率",
            "",
            "4. **博弈论纳什均衡**:",
            "   对于多人关卡，寻找策略组合 (s₁*, s₂*, ..., sₙ*) 使得:",
            "   πᵢ(sᵢ*, s₋ᵢ*) ≥ πᵢ(sᵢ, s₋ᵢ*), ∀sᵢ, ∀i",
            "",
            "🔧 求解算法总结:",
            "",
            "• **确定性关卡 (1,2,5)**:",
            "  - 使用动态规划精确求解",
            "  - 状态空间: 时间×位置×资源状态",
            "  - 保证全局最优解",
            "",
            "• **随机性关卡 (3,4,6)**:",
            "  - 使用保守贪心策略",
            "  - 基于最坏情况的资源估算",
            "  - 多策略比较选择",
            "",
            "💡 模型创新点:",
            "",
            "1. **多层次决策结构**:",
            "   • 战略层: 路径选择 (direct/mining/village)",
            "   • 战术层: 资源分配和时间安排",
            "   • 操作层: 每日具体行动",
            "",
            "2. **不确定性处理**:",
            "   • 天气已知: 完全信息动态规划",
            "   • 天气未知: 鲁棒优化 + 保守策略",
            "",
            "3. **多目标权衡**:",
            "   • 时间效率 vs 收益最大化",
            "   • 风险控制 vs 收益追求",
            "   • 资源消耗 vs 安全边际",
            "",
            "📈 计算复杂度分析:",
            "",
            "• **最优解复杂度**: 所有关卡都是NP-Hard问题",
            "• **实际求解**: 通过启发式算法在合理时间内获得高质量解",
            "• **可扩展性**: 算法可扩展到更大规模的地图和更多玩家",
            "",
            "🚀 实际应用价值:",
            "",
            "1. **物流优化**: 路径规划、资源调度",
            "2. **项目管理**: 时间-成本-质量权衡",
            "3. **风险管理**: 不确定环境下的决策",
            "4. **博弈分析**: 竞争环境下的策略选择",
            "",
            "=" * 120,
            f"完整数学建模报告生成完成 - {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            "=" * 120
        ])

        return lines

    def save_complete_models(self, filename: str = None) -> str:
        """保存完整数学模型到文件"""
        if filename is None:
            timestamp = datetime.now().strftime("%Y%m%d_%H%M%S")
            filename = f"complete_mathematical_models_{timestamp}.txt"

        model_content = self.generate_all_models()

        filepath = f"results/{filename}"
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(model_content)

        return filepath


def main():
    """主函数"""
    print("🔄 正在生成完整数学建模报告...")

    generator = CompleteMathematicalModelGenerator()
    report_file = generator.save_complete_models()

    print(f"✅ 完整数学建模报告已生成: {report_file}")
    print(f"📊 报告包含:")
    print(f"   • 所有6个关卡的完整数学模型")
    print(f"   • 详细的决策变量定义")
    print(f"   • 完整的约束条件系统")
    print(f"   • 目标函数和求解方法")
    print(f"   • 复杂度分析和模型对比")
    print(f"   • 数学建模总结和应用价值")

    # 同时生成JSON格式的模型数据
    print(f"\n🔄 正在生成JSON格式的数学模型数据...")

    config = GameConfig()
    for level in range(1, 7):
        level_config = config.get_level_config(level)
        if level_config:
            # 生成结构化的数学模型数据
            model_data = {
                "level": level,
                "problem_type": "确定性MINLP" if level_config.weather_known else "随机性MINLP",
                "variables": {
                    "spatial": f"x_ij^t, s_i^t, l_i^t for i,j ∈ {{1,...,{len(level_config.map_nodes)}}}",
                    "resource": "w_t, f_t, w_0, f_0",
                    "action": "m_i^t, b_i^t",
                    "state": "M_t, arrived_t"
                },
                "parameters": {
                    "nodes": len(level_config.map_nodes),
                    "time_limit": level_config.deadline_days,
                    "initial_money": level_config.initial_money,
                    "players": level_config.players,
                    "weather_known": level_config.weather_known
                },
                "objective": "max Z = M_T + 0.5·P_w·w_T + 0.5·P_f·f_T",
                "solution_method": "动态规划" if level_config.weather_known else "贪心策略",
                "complexity": f"O(N²·T·W)" if level_config.weather_known else "O(K·T·N)"
            }

            json_file = f"results/mathematical_model_level_{level}.json"
            with open(json_file, 'w', encoding='utf-8') as f:
                json.dump(model_data, f, ensure_ascii=False, indent=2)

    print(f"✅ JSON格式数学模型已生成: results/mathematical_model_level_*.json")


if __name__ == "__main__":
    main()
    
    def _generate_parameters(self, level: int, level_config) -> List[str]:
        """生成参数定义"""
        lines = []
        
        # 基础参数
        lines.extend([
            f"📊 参数定义:",
            f"   地图参数:",
            f"     N = {len(level_config.map_nodes)}           : 节点总数",
            f"     T = {level_config.deadline_days}            : 时间限制 (天)",
            f"     S = {level_config.start_node}             : 起点节点",
            f"     E = {level_config.end_node}             : 终点节点",
            f"     M = {level_config.mine_nodes}        : 矿山节点集合",
            f"     V = {level_config.village_nodes}      : 村庄节点集合",
            "",
            f"   经济参数:",
            f"     M_0 = {level_config.initial_money}        : 初始资金 (元)",
            f"     P_w = {level_config.water_price}             : 水价格 (元/箱)",
            f"     P_f = {level_config.food_price}            : 食物价格 (元/箱)",
            f"     R = {level_config.base_income}          : 挖矿基础收益 (元/天)",
            "",
            f"   物理参数:",
            f"     W_max = {level_config.max_weight}       : 负重上限 (kg)",
            f"     w_weight = {level_config.water_weight}         : 水重量 (kg/箱)",
            f"     f_weight = {level_config.food_weight}         : 食物重量 (kg/箱)",
            ""
        ])
        
        # 消耗参数
        lines.extend([
            f"   消耗参数:",
        ])
        
        for weather, consumption in level_config.consumption.items():
            lines.append(f"     {weather}天气: 水{consumption['water']}箱/天, 食物{consumption['food']}箱/天")
        
        lines.extend([
            f"     α = {level_config.move_factor}           : 移动消耗倍数",
            f"     β = {getattr(level_config, 'mine_consumption_multiplier', 1.5)}           : 挖矿消耗倍数",
            ""
        ])
        
        # 天气参数
        if level_config.weather_known:
            lines.extend([
                f"   天气参数 (已知):",
                f"     weather_t ∈ {{'晴朗','高温','沙暴'}} : 第t天天气状况",
                f"     天气预报: {len(level_config.weather_forecast)}天完整预报",
            ])
        else:
            lines.extend([
                f"   天气参数 (未知):",
                f"     weather_t ∈ {{'晴朗','高温','沙暴'}} : 第t天天气状况 (仅知当天)",
                f"     概率分布: 需要基于历史数据估算",
            ])
        
        lines.append("")
        
        return lines
