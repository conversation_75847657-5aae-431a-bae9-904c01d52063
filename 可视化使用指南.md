# 穿越沙漠项目可视化使用指南

## 🚀 快速开始

### 1. 查看已生成的图表
项目已经为您生成了基础的可视化图表：

```
charts/
├── level_comparison.png    # 关卡对比分析图
└── trend_analysis.png      # 趋势分析图
```

这些图表可以直接在您的报告中使用。

### 2. 查看流程图
流程图使用Mermaid语法编写，您可以通过以下方式查看：

#### 方法一：在线查看
1. 访问 https://mermaid.live/
2. 复制 `visualization/generate_flowcharts.md` 中的Mermaid代码
3. 粘贴到在线编辑器中即可看到交互式流程图

#### 方法二：VS Code查看
1. 安装 "Mermaid Preview" 插件
2. 打开 `visualization/generate_flowcharts.md` 文件
3. 使用预览功能查看流程图

#### 方法三：GitHub查看
如果您的项目在GitHub上，Mermaid图表会自动渲染显示。

## 📊 可用的图表类型

### 已生成的图表

#### 1. 关卡对比分析图 (`charts/level_comparison.png`)
- **内容**: 净利润、最终资金、用时、策略分布
- **用途**: 展示各关卡的整体表现对比
- **特点**: 多维度对比，直观易懂

#### 2. 趋势分析图 (`charts/trend_analysis.png`)
- **内容**: 各项指标的变化趋势和效率分析
- **用途**: 分析关卡间的发展趋势
- **特点**: 趋势线图，便于发现规律

### 可生成的图表

如果您需要更多图表，可以运行以下脚本：

```bash
# 生成完整图表集（如果Python环境正常）
python visualization/generate_charts.py

# 或者生成基础图表
python visualization/simple_charts.py

# 运行数据分析
python visualization/data_analysis.py
```

可能生成的额外图表包括：
- 资源消耗分析图
- 策略效果对比图
- 数学模型汇总表
- 风险评估图表

## 🔄 流程图说明

### 1. 算法整体流程图
- **用途**: 展示完整的求解过程
- **适用场景**: 算法原理介绍、系统架构说明
- **关键节点**: 配置读取、策略选择、约束验证、结果输出

### 2. 单关卡求解流程图
- **用途**: 详细展示单个关卡的处理逻辑
- **适用场景**: 技术实现细节、算法步骤说明
- **关键节点**: 路径规划、策略枚举、资源计算

### 3. 决策树流程图
- **用途**: 展示实时决策的逻辑结构
- **适用场景**: 策略选择逻辑、条件判断说明
- **关键节点**: 天气判断、策略选择、资源检查

### 4. 数据流图
- **用途**: 展示系统各组件间的数据流动
- **适用场景**: 系统设计、数据处理流程
- **关键节点**: 数据输入、处理模块、结果输出

## 📝 在报告中使用图表的建议

### 1. 图表选择
- **概述部分**: 使用关卡对比分析图
- **结果分析**: 使用趋势分析图
- **方法介绍**: 使用算法流程图
- **技术实现**: 使用数据流图

### 2. 图表说明
每个图表都应该包含：
- **图表标题**: 简洁明确的标题
- **图表说明**: 解释图表内容和含义
- **关键发现**: 从图表中得出的重要结论
- **数据来源**: 说明数据的来源和时间

### 3. 图表格式
- **分辨率**: 所有PNG图表都是300 DPI高清格式
- **尺寸**: 适合A4纸张打印和电子文档显示
- **字体**: 支持中文显示，无乱码问题

## 🛠️ 自定义图表

如果您需要自定义图表，可以：

### 1. 修改现有脚本
编辑 `visualization/simple_charts.py` 文件：
- 调整图表样式和颜色
- 修改图表标题和标签
- 添加新的数据维度

### 2. 创建新的分析
基于现有数据创建新的分析：
- 使用 `results/` 目录下的JSON和CSV文件
- 参考现有脚本的数据加载方式
- 使用matplotlib或seaborn创建图表

### 3. 修改流程图
编辑 `visualization/generate_flowcharts.md` 文件：
- 使用Mermaid语法
- 调整节点样式和连接关系
- 添加新的流程步骤

## 📋 数据文件说明

### 原始数据文件
```
results/
├── level_*_summary.json        # 关卡汇总结果
├── level_*_daily_log.csv       # 每日详细记录
├── level_*_mathematical_model.json # 数学模型定义
└── all_levels_summary_report.txt   # 文本格式汇总
```

### 数据字段说明
- **summary.json**: 包含策略类型、最终资金、净利润、用时等
- **daily_log.csv**: 包含每日位置、天气、行动、消耗、收入等
- **mathematical_model.json**: 包含模型类型、变量、约束、复杂度等

## ❓ 常见问题

### Q1: 图表显示乱码怎么办？
A: 确保系统安装了中文字体，或者修改脚本中的字体设置。

### Q2: 无法生成新图表怎么办？
A: 检查Python环境和依赖库安装，或者直接使用已生成的图表。

### Q3: 流程图无法显示怎么办？
A: 使用在线Mermaid编辑器 https://mermaid.live/ 查看。

### Q4: 如何获得更高质量的图表？
A: 现有图表已经是300 DPI高清格式，适合打印和展示。

## 📞 技术支持

如果您在使用过程中遇到问题：
1. 查看错误信息和日志
2. 检查数据文件是否完整
3. 确认Python环境和依赖库
4. 参考现有的成功示例

---

**最后更新**: 2025-01-02  
**适用版本**: 穿越沙漠数学建模项目 v1.0
