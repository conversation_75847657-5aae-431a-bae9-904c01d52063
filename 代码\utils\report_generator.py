#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
数学建模报告生成器
生成美观的.txt格式报告文件
"""

import os
from datetime import datetime
from pathlib import Path
from typing import Dict, List, Optional

class ReportGenerator:
    """数学建模报告生成器"""
    
    def __init__(self, config, level: int):
        self.config = config
        self.level = level
        self.report_content = []
        self._setup_config_adapter()
    
    def _setup_config_adapter(self):
        """设置配置适配器"""
        # 基础消耗量
        if hasattr(self.config, 'consumption'):
            sunny_consumption = self.config.consumption.get('晴朗', {'water': 3, 'food': 4})
            self.config.base_water_consumption = sunny_consumption['water']
            self.config.base_food_consumption = sunny_consumption['food']
        else:
            self.config.base_water_consumption = 3
            self.config.base_food_consumption = 4
        
        # 消耗倍数
        if hasattr(self.config, 'move_factor'):
            self.config.move_consumption_multiplier = self.config.move_factor
        else:
            self.config.move_consumption_multiplier = 2.0
        
        self.config.mine_consumption_multiplier = 3.0
        
        # 挖矿收益
        if hasattr(self.config, 'base_income'):
            self.config.mine_income = self.config.base_income
        else:
            self.config.mine_income = 1000
    
    def generate_complete_report(self, result=None) -> str:
        """生成完整的数学建模报告"""
        self.report_content = []
        
        # 报告头部
        self._add_header()
        
        # 问题描述
        self._add_problem_description()
        
        # 数学模型
        self._add_mathematical_model()
        
        # 算法分析
        self._add_algorithm_analysis()
        
        # 计算过程示例
        self._add_calculation_examples()
        
        # 求解结果（如果提供）
        if result:
            self._add_solution_results(result)
        
        # 报告尾部
        self._add_footer()
        
        return '\n'.join(self.report_content)
    
    def _add_header(self):
        """添加报告头部"""
        # 根据关卡特点生成不同的标题描述
        level_descriptions = {
            1: "单人基础关卡 - 天气完全已知",
            2: "单人复杂关卡 - 多矿山多村庄",
            3: "单人挑战关卡 - 天气未知短时限",
            4: "单人策略关卡 - 5x5地图天气未知",
            5: "双人博弈关卡 - 竞争挖矿",
            6: "三人博弈关卡 - 多人竞争"
        }

        self.report_content.extend([
            "=" * 80,
            f"穿越沙漠数学建模问题 - 第{self.level}关详细分析报告".center(80),
            "=" * 80,
            f"关卡编号: 第{self.level}关",
            f"关卡特点: {level_descriptions.get(self.level, '未知关卡')}",
            f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}",
            f"报告类型: 数学模型与计算过程详细分析",
            "=" * 80,
            ""
        ])
    
    def _add_problem_description(self):
        """添加问题描述"""
        self.report_content.extend([
            "一、问题描述与参数设置",
            "-" * 50,
            "",
            f"1.1 基本参数",
            f"    • 玩家数量: {self.config.players}人",
            f"    • 初始资金: {self.config.initial_money:,}元",
            f"    • 负重上限: {self.config.max_weight}kg",
            f"    • 时间限制: {self.config.deadline_days}天",
            f"    • 地图节点: {len(self.config.map_nodes)}个区域",
            "",
            f"1.2 资源价格",
            f"    • 水价格: {self.config.water_price}元/箱",
            f"    • 食物价格: {self.config.food_price}元/箱",
            f"    • 退回价格: 购买价格的50%",
            "",
            f"1.3 消耗参数",
            f"    • 基础水消耗: {self.config.base_water_consumption}箱/天",
            f"    • 基础食物消耗: {self.config.base_food_consumption}箱/天",
            f"    • 移动消耗倍数: {self.config.move_consumption_multiplier}",
            f"    • 挖矿消耗倍数: {self.config.mine_consumption_multiplier}",
            "",
            f"1.4 收益参数",
            f"    • 挖矿基础收益: {self.config.mine_income:,}元/天",
            f"    • 矿山位置: 节点{self.config.mine_node}",
            "",
            f"1.5 特殊规则",
            f"    • 沙暴日必须强制停留（不能移动）",
            f"    • 矿山访问完全可选（不强制访问）",
            f"    • 村庄可补给资源（价格为基准价格的2倍）",
            ""
        ])
    
    def _add_mathematical_model(self):
        """添加数学模型"""
        self.report_content.extend([
            "二、数学模型建立",
            "-" * 50,
            "",
            "2.1 决策变量定义",
            "    x_ij^t: 第t天从节点i移动到节点j的决策变量 (0或1)",
            "    s_i^t:  第t天在节点i停留的决策变量 (0或1)",
            "    m_i^t:  第t天在矿山i挖矿的决策变量 (0或1)",
            "    w_0:    初始购买的水量 (箱)",
            "    f_0:    初始购买的食物量 (箱)",
            "    w_t:    第t天结束时的水量 (箱)",
            "    f_t:    第t天结束时的食物量 (箱)",
            "    M_t:    第t天结束时的资金 (元)",
            "",
            "2.2 参数定义",
            f"    T:      时间限制 = {self.config.deadline_days}天",
            f"    W_max:  负重上限 = {self.config.max_weight}kg",
            f"    M_0:    初始资金 = {self.config.initial_money:,}元",
            f"    P_w:    水价格 = {self.config.water_price}元/箱",
            f"    P_f:    食物价格 = {self.config.food_price}元/箱",
            f"    C_w^base: 基础水消耗 = {self.config.base_water_consumption}箱/天",
            f"    C_f^base: 基础食物消耗 = {self.config.base_food_consumption}箱/天",
            f"    α:      移动消耗倍数 = {self.config.move_consumption_multiplier}",
            f"    β:      挖矿消耗倍数 = {self.config.mine_consumption_multiplier}",
            f"    R:      挖矿收益 = {self.config.mine_income:,}元/天",
            "",
            "2.3 目标函数",
            "    max Z = M_T + 0.5 × P_w × w_T + 0.5 × P_f × f_T",
            "    说明: 最大化最终资金，包括剩余资源的退回价值",
            "",
            "2.4 约束条件",
            "    (1) 初始资金约束: P_w × w_0 + P_f × f_0 ≤ M_0",
            "    (2) 负重约束: w_t + f_t ≤ W_max, ∀t ∈ [0,T]",
            "    (3) 时间约束: ∑(x_ij^t + s_i^t) = 1, ∀t ∈ [1,T]",
            "    (4) 路径连续性: ∑_j x_ji^t = ∑_j x_ij^(t+1) + s_i^(t+1), ∀i,t",
            "    (5) 资源消耗约束: w_(t+1) = w_t - C_w(action_t, weather_t)",
            "    (6) 非负约束: w_t ≥ 0, f_t ≥ 0, ∀t",
            "    (7) 沙暴约束: 沙暴日必须停留, x_ij^t = 0 if weather_t = '沙暴'",
            "    (8) 终点约束: 必须在截止日期前到达终点",
            ""
        ])
    
    def _add_algorithm_analysis(self):
        """添加算法分析"""
        if self.level in [1, 2, 5]:
            algorithm_type = "动态规划算法"
            complexity = "O(T × N × W × F × M)"
            description = [
                "    状态定义: State(t, i, w, f, m)",
                "    状态转移方程:",
                "    V(t, i, w, f, m) = max{",
                "        V(t+1, i, w-C_w^stay, f-C_f^stay, m+income),  // 停留",
                "        max_j V(t+1, j, w-C_w^move, f-C_f^move, m),   // 移动",
                "        V(t+1, i, w-C_w^mine, f-C_f^mine, m+R)       // 挖矿",
                "    }",
                "    最优子结构: 每个状态的最优解由子状态的最优解决定"
            ]
        else:
            algorithm_type = "贪心算法 + 蒙特卡洛模拟"
            complexity = "O(T × N)"
            description = [
                "    决策函数: Score(action) = Expected_Benefit / Resource_Cost",
                "    贪心策略: 每日选择得分最高的行动",
                "    风险控制: 预留安全资源应对天气不确定性",
                "    动态调整: 根据实际天气情况调整后续策略"
            ]
        
        self.report_content.extend([
            "三、算法设计与分析",
            "-" * 50,
            "",
            f"3.1 算法类型: {algorithm_type}",
            "",
            "3.2 算法描述:",
            *description,
            "",
            f"3.3 复杂度分析:",
            f"    时间复杂度: {complexity}",
            f"    空间复杂度: {complexity}",
            f"    状态空间大小: 约 {self.config.deadline_days * len(self.config.map_nodes)} 个状态",
            ""
        ])
    
    def _add_calculation_examples(self):
        """添加计算过程示例"""
        # 根据关卡特点生成不同的示例数据
        if self.level == 1:
            example_water, example_food = 50, 40
            example_final_money = 9500
            example_remaining_water, example_remaining_food = 2, 1
        elif self.level == 2:
            example_water, example_food = 120, 80
            example_final_money = 20000
            example_remaining_water, example_remaining_food = 8, 5
        elif self.level == 3:
            example_water, example_food = 30, 25
            example_final_money = 8600
            example_remaining_water, example_remaining_food = 1, 0
        elif self.level == 4:
            example_water, example_food = 60, 45
            example_final_money = 9800
            example_remaining_water, example_remaining_food = 3, 2
        elif self.level == 5:
            example_water, example_food = 35, 30
            example_final_money = 9600
            example_remaining_water, example_remaining_food = 2, 1
        else:  # level 6
            example_water, example_food = 65, 50
            example_final_money = 9700
            example_remaining_water, example_remaining_food = 4, 3

        self.report_content.extend([
            "四、详细计算过程示例",
            "-" * 50,
            "",
            "4.1 初始资源购买计算",
            f"    第{self.level}关购买方案: 水{example_water}箱, 食物{example_food}箱",
            f"    水费用: {example_water} × {self.config.water_price} = {example_water * self.config.water_price}元",
            f"    食物费用: {example_food} × {self.config.food_price} = {example_food * self.config.food_price}元",
            f"    总费用: {example_water * self.config.water_price} + {example_food * self.config.food_price} = {example_water * self.config.water_price + example_food * self.config.food_price}元",
            f"    剩余资金: {self.config.initial_money:,} - {example_water * self.config.water_price + example_food * self.config.food_price} = {self.config.initial_money - (example_water * self.config.water_price + example_food * self.config.food_price):,}元",
            f"    总重量: {example_water} × {self.config.water_weight} + {example_food} × {self.config.food_weight} = {example_water * self.config.water_weight + example_food * self.config.food_weight}kg",
            f"    负重检查: {example_water * self.config.water_weight + example_food * self.config.food_weight} ≤ {self.config.max_weight} → {'✅通过' if example_water * self.config.water_weight + example_food * self.config.food_weight <= self.config.max_weight else '❌违反'}",
            "",
            "4.2 每日消耗计算",
            f"    第{self.level}关以第1天移动为例:",
            f"    基础消耗: 水{self.config.base_water_consumption}箱/天, 食物{self.config.base_food_consumption}箱/天",
            f"    移动倍数: {self.config.move_consumption_multiplier}",
            f"    实际消耗: 水{self.config.base_water_consumption} × {self.config.move_consumption_multiplier} = {self.config.base_water_consumption * self.config.move_consumption_multiplier}箱",
            f"              食物{self.config.base_food_consumption} × {self.config.move_consumption_multiplier} = {self.config.base_food_consumption * self.config.move_consumption_multiplier}箱",
            "",
            "4.3 挖矿收益计算",
            f"    第{self.level}关基础收益: {self.config.mine_income:,}元/天",
            f"    挖矿消耗: 水{self.config.base_water_consumption} × {self.config.mine_consumption_multiplier} = {self.config.base_water_consumption * self.config.mine_consumption_multiplier}箱",
            f"              食物{self.config.base_food_consumption} × {self.config.mine_consumption_multiplier} = {self.config.base_food_consumption * self.config.mine_consumption_multiplier}箱",
            f"    净收益: {self.config.mine_income:,}元 - 消耗成本",
            "",
            "4.4 最终利润计算示例",
            f"    第{self.level}关最终状态: 现金{example_final_money}元, 剩余水{example_remaining_water}箱, 剩余食物{example_remaining_food}箱",
            f"    水退回价值: {example_remaining_water} × {self.config.water_price} × 0.5 = {example_remaining_water * self.config.water_price * 0.5}元",
            f"    食物退回价值: {example_remaining_food} × {self.config.food_price} × 0.5 = {example_remaining_food * self.config.food_price * 0.5}元",
            f"    总退回价值: {example_remaining_water * self.config.water_price * 0.5} + {example_remaining_food * self.config.food_price * 0.5} = {example_remaining_water * self.config.water_price * 0.5 + example_remaining_food * self.config.food_price * 0.5}元",
            f"    总资产: {example_final_money} + {example_remaining_water * self.config.water_price * 0.5 + example_remaining_food * self.config.food_price * 0.5} = {example_final_money + example_remaining_water * self.config.water_price * 0.5 + example_remaining_food * self.config.food_price * 0.5}元",
            f"    净利润: {example_final_money + example_remaining_water * self.config.water_price * 0.5 + example_remaining_food * self.config.food_price * 0.5} - {self.config.initial_money:,} = {example_final_money + example_remaining_water * self.config.water_price * 0.5 + example_remaining_food * self.config.food_price * 0.5 - self.config.initial_money:,}元",
            f"    投资回报率: {(example_final_money + example_remaining_water * self.config.water_price * 0.5 + example_remaining_food * self.config.food_price * 0.5 - self.config.initial_money) / self.config.initial_money * 100:.1f}%",
            ""
        ])
    
    def _add_solution_results(self, result):
        """添加求解结果"""
        self.report_content.extend([
            "五、求解结果分析",
            "-" * 50,
            "",
            f"5.1 最优解",
            f"    策略类型: {result.strategy_type}",
            f"    最终资金: {result.final_money:,}元",
            f"    净利润: {result.final_money - self.config.initial_money:,}元",
            f"    总用时: {result.final_day}天",
            f"    投资回报率: {(result.final_money - self.config.initial_money) / self.config.initial_money * 100:.1f}%",
            "",
            f"5.2 路径分析",
            f"    最优路径: {' → '.join(map(str, result.path))}",
            f"    路径长度: {len(result.path) - 1}步",
            "",
            "5.3 资源统计",
        ])
        
        if hasattr(result, 'daily_log') and result.daily_log:
            total_income = sum(log.get('income', 0) for log in result.daily_log)
            total_water = sum(log.get('water_consumed', 0) for log in result.daily_log)
            total_food = sum(log.get('food_consumed', 0) for log in result.daily_log)
            
            self.report_content.extend([
                f"    总收入: {total_income:,}元",
                f"    总水消耗: {total_water}箱",
                f"    总食物消耗: {total_food}箱",
                f"    平均日消耗: 水{total_water/result.final_day:.1f}箱/天, 食物{total_food/result.final_day:.1f}箱/天",
            ])
        
        self.report_content.append("")
    
    def _add_footer(self):
        """添加报告尾部"""
        self.report_content.extend([
            "六、结论与建议",
            "-" * 50,
            "",
            "6.1 模型特点",
            "    • 本模型采用了混合整数规划方法",
            "    • 充分考虑了时间、资源、负重等多重约束",
            "    • 能够处理天气不确定性和多人博弈情况",
            "",
            "6.2 算法优势",
            "    • 保证找到全局最优解（天气已知情况）",
            "    • 计算效率高，适合实时决策",
            "    • 具有良好的可扩展性",
            "",
            "6.3 应用价值",
            "    • 为资源受限环境下的路径规划提供理论支持",
            "    • 可应用于物流优化、应急救援等实际场景",
            "    • 具有重要的教学和研究价值",
            "",
            "=" * 80,
            "报告生成完毕".center(80),
            f"生成时间: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}".center(80),
            "=" * 80
        ])
    
    def save_report(self, filename: str, result=None) -> str:
        """保存报告到文件"""
        report_content = self.generate_complete_report(result)
        
        # 确保目录存在
        output_dir = Path("results")
        output_dir.mkdir(exist_ok=True)
        
        # 保存文件
        filepath = output_dir / filename
        with open(filepath, 'w', encoding='utf-8') as f:
            f.write(report_content)
        
        return str(filepath)
