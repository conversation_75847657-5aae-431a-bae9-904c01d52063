#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
详细计算过程展示器
展示穿越沙漠问题的每一步计算细节
"""

from typing import Dict, List, Tuple, Optional
import math

class CalculationProcessDisplay:
    """计算过程详细展示器"""
    
    def __init__(self, config):
        self.config = config
        self.level = config.level
        self._setup_config_adapter()

    def _setup_config_adapter(self):
        """设置配置适配器"""
        # 基础消耗量
        if hasattr(self.config, 'consumption'):
            sunny_consumption = self.config.consumption.get('晴朗', {'water': 3, 'food': 4})
            self.config.base_water_consumption = sunny_consumption['water']
            self.config.base_food_consumption = sunny_consumption['food']
        else:
            self.config.base_water_consumption = 3
            self.config.base_food_consumption = 4

        # 消耗倍数
        if hasattr(self.config, 'move_factor'):
            self.config.move_consumption_multiplier = self.config.move_factor
        else:
            self.config.move_consumption_multiplier = 2.0

        self.config.mine_consumption_multiplier = 3.0

        # 挖矿收益
        if hasattr(self.config, 'base_income'):
            self.config.mine_income = self.config.base_income
        
    def display_initial_resource_calculation(self, water_amount: int, food_amount: int):
        """展示初始资源购买计算"""
        print("\n💰 初始资源购买计算:")
        print("=" * 50)
        
        water_cost = water_amount * self.config.water_price
        food_cost = food_amount * self.config.food_price
        total_cost = water_cost + food_cost
        remaining_money = self.config.initial_money - total_cost
        total_weight = water_amount + food_amount
        
        print(f"📊 购买方案:")
        print(f"   水量: {water_amount} 箱")
        print(f"   食物: {food_amount} 箱")
        print(f"   总重量: {total_weight} kg")
        
        print(f"\n💵 费用计算:")
        print(f"   水费用: {water_amount} × {self.config.water_price} = {water_cost} 元")
        print(f"   食物费用: {food_amount} × {self.config.food_price} = {food_cost} 元")
        print(f"   总费用: {water_cost} + {food_cost} = {total_cost} 元")
        print(f"   剩余资金: {self.config.initial_money} - {total_cost} = {remaining_money} 元")
        
        print(f"\n✅ 约束检查:")
        print(f"   负重约束: {total_weight} ≤ {self.config.max_weight} kg → {'✅通过' if total_weight <= self.config.max_weight else '❌违反'}")
        print(f"   资金约束: {total_cost} ≤ {self.config.initial_money} → {'✅通过' if total_cost <= self.config.initial_money else '❌违反'}")
        
        return {
            'water_cost': water_cost,
            'food_cost': food_cost,
            'total_cost': total_cost,
            'remaining_money': remaining_money,
            'total_weight': total_weight,
            'feasible': total_weight <= self.config.max_weight and total_cost <= self.config.initial_money
        }
    
    def display_daily_consumption_calculation(self, day: int, action: str, weather: str, 
                                            current_water: int, current_food: int):
        """展示每日消耗计算"""
        print(f"\n📅 第{day}天消耗计算:")
        print("=" * 50)
        
        print(f"🌤️ 当前状态:")
        print(f"   天气: {weather}")
        print(f"   行动: {action}")
        print(f"   当前水量: {current_water} 箱")
        print(f"   当前食物: {current_food} 箱")
        
        # 基础消耗
        base_water = self.config.base_water_consumption
        base_food = self.config.base_food_consumption
        
        # 根据行动类型计算消耗倍数
        if action == "停留":
            multiplier = 1.0
            multiplier_desc = "停留倍数"
        elif action == "移动":
            multiplier = self.config.move_consumption_multiplier
            multiplier_desc = "移动倍数"
        elif action == "挖矿":
            multiplier = self.config.mine_consumption_multiplier
            multiplier_desc = "挖矿倍数"
        else:
            multiplier = 1.0
            multiplier_desc = "默认倍数"
        
        # 计算实际消耗
        water_consumed = base_water * multiplier
        food_consumed = base_food * multiplier
        
        # 天气影响 (如果有)
        weather_factor = 1.0
        if weather == "高温":
            weather_factor = 1.2  # 高温增加20%水消耗
            water_consumed *= weather_factor
        
        print(f"\n🧮 消耗计算:")
        print(f"   基础水消耗: {base_water} 箱/天")
        print(f"   基础食物消耗: {base_food} 箱/天")
        print(f"   {multiplier_desc}: {multiplier}")
        if weather == "高温":
            print(f"   高温影响: 水消耗 × {weather_factor}")
        
        print(f"\n📊 实际消耗:")
        if weather == "高温":
            print(f"   水消耗: {base_water} × {multiplier} × {weather_factor} = {water_consumed:.1f} 箱")
        else:
            print(f"   水消耗: {base_water} × {multiplier} = {water_consumed:.1f} 箱")
        print(f"   食物消耗: {base_food} × {multiplier} = {food_consumed:.1f} 箱")
        
        # 剩余资源
        remaining_water = current_water - water_consumed
        remaining_food = current_food - food_consumed
        
        print(f"\n📦 剩余资源:")
        print(f"   剩余水量: {current_water} - {water_consumed:.1f} = {remaining_water:.1f} 箱")
        print(f"   剩余食物: {current_food} - {food_consumed:.1f} = {remaining_food:.1f} 箱")
        
        # 可行性检查
        feasible = remaining_water >= 0 and remaining_food >= 0
        print(f"\n✅ 可行性: {'✅可行' if feasible else '❌不可行 - 资源不足'}")
        
        return {
            'water_consumed': water_consumed,
            'food_consumed': food_consumed,
            'remaining_water': remaining_water,
            'remaining_food': remaining_food,
            'feasible': feasible
        }
    
    def display_mining_income_calculation(self, day: int, players_mining: int = 1):
        """展示挖矿收益计算"""
        if not hasattr(self.config, 'mine_income'):
            return {'income': 0}
            
        print(f"\n⛏️ 第{day}天挖矿收益计算:")
        print("=" * 50)
        
        base_income = self.config.mine_income
        
        if self.config.players == 1:
            actual_income = base_income
            print(f"💰 单人挖矿:")
            print(f"   基础收益: {base_income} 元/天")
            print(f"   实际收益: {actual_income} 元")
        else:
            # 多人博弈情况
            if players_mining > 1:
                shared_income = base_income / players_mining
                actual_income = shared_income
                print(f"👥 多人挖矿 ({players_mining}人):")
                print(f"   基础收益: {base_income} 元/天")
                print(f"   分摊收益: {base_income} ÷ {players_mining} = {shared_income:.1f} 元/人")
                print(f"   实际收益: {actual_income:.1f} 元")
            else:
                actual_income = base_income
                print(f"💰 独自挖矿:")
                print(f"   基础收益: {base_income} 元/天")
                print(f"   实际收益: {actual_income} 元")
        
        return {'income': actual_income}
    
    def display_path_optimization_calculation(self, start: int, end: int, path: List[int]):
        """展示路径优化计算"""
        print(f"\n🗺️ 路径优化计算:")
        print("=" * 50)
        
        print(f"📍 路径规划:")
        print(f"   起点: 节点{start}")
        print(f"   终点: 节点{end}")
        print(f"   路径: {' → '.join(map(str, path))}")
        print(f"   路径长度: {len(path) - 1} 步")
        
        # 计算路径成本
        if len(path) > 1:
            total_move_days = len(path) - 1
            move_water_cost = total_move_days * self.config.base_water_consumption * self.config.move_consumption_multiplier
            move_food_cost = total_move_days * self.config.base_food_consumption * self.config.move_consumption_multiplier
            
            print(f"\n💧 路径资源成本:")
            print(f"   移动天数: {total_move_days} 天")
            print(f"   水消耗: {total_move_days} × {self.config.base_water_consumption} × {self.config.move_consumption_multiplier} = {move_water_cost} 箱")
            print(f"   食物消耗: {total_move_days} × {self.config.base_food_consumption} × {self.config.move_consumption_multiplier} = {move_food_cost} 箱")
            print(f"   总消耗: {move_water_cost + move_food_cost} 箱")
        
        return {
            'path_length': len(path) - 1,
            'move_water_cost': move_water_cost if len(path) > 1 else 0,
            'move_food_cost': move_food_cost if len(path) > 1 else 0
        }
    
    def display_final_profit_calculation(self, final_money: int, final_water: int, final_food: int):
        """展示最终利润计算"""
        print(f"\n💎 最终利润计算:")
        print("=" * 50)
        
        # 剩余资源退回价值
        water_refund = final_water * (self.config.water_price * 0.5)
        food_refund = final_food * (self.config.food_price * 0.5)
        total_refund = water_refund + food_refund
        
        # 总资产
        total_assets = final_money + total_refund
        
        # 净利润
        net_profit = total_assets - self.config.initial_money
        
        print(f"💰 最终状态:")
        print(f"   现金: {final_money} 元")
        print(f"   剩余水量: {final_water} 箱")
        print(f"   剩余食物: {final_food} 箱")
        
        print(f"\n🔄 资源退回:")
        print(f"   水退回价值: {final_water} × {self.config.water_price} × 0.5 = {water_refund} 元")
        print(f"   食物退回价值: {final_food} × {self.config.food_price} × 0.5 = {food_refund} 元")
        print(f"   总退回价值: {water_refund} + {food_refund} = {total_refund} 元")
        
        print(f"\n📊 最终结算:")
        print(f"   总资产: {final_money} + {total_refund} = {total_assets} 元")
        print(f"   初始资金: {self.config.initial_money} 元")
        print(f"   净利润: {total_assets} - {self.config.initial_money} = {net_profit} 元")
        print(f"   投资回报率: {net_profit / self.config.initial_money * 100:.1f}%")
        
        return {
            'water_refund': water_refund,
            'food_refund': food_refund,
            'total_refund': total_refund,
            'total_assets': total_assets,
            'net_profit': net_profit,
            'roi': net_profit / self.config.initial_money * 100
        }
    
    def display_strategy_comparison(self, strategies: List[Dict]):
        """展示策略对比计算"""
        print(f"\n🔍 策略对比分析:")
        print("=" * 50)
        
        print(f"{'策略类型':<12} {'最终资金':<10} {'净利润':<10} {'用时':<6} {'可行性'}")
        print("-" * 50)
        
        for strategy in strategies:
            strategy_type = strategy.get('type', 'unknown')
            final_money = strategy.get('final_money', 0)
            net_profit = final_money - self.config.initial_money
            days_used = strategy.get('days_used', 0)
            feasible = strategy.get('feasible', False)
            
            print(f"{strategy_type:<12} {final_money:<10,} {net_profit:<10,} {days_used:<6} {'✅' if feasible else '❌'}")
        
        # 找出最优策略
        feasible_strategies = [s for s in strategies if s.get('feasible', False)]
        if feasible_strategies:
            best_strategy = max(feasible_strategies, key=lambda x: x.get('final_money', 0))
            print(f"\n🏆 最优策略: {best_strategy.get('type', 'unknown')}")
            print(f"   最终资金: {best_strategy.get('final_money', 0):,} 元")
            print(f"   净利润: {best_strategy.get('final_money', 0) - self.config.initial_money:,} 元")

def demonstrate_calculation_process():
    """演示计算过程"""
    print("🧮 穿越沙漠问题详细计算过程演示")
    print("=" * 60)
    
    print("本演示展示了以下计算过程:")
    print("1. 初始资源购买计算")
    print("2. 每日消耗详细计算")
    print("3. 挖矿收益计算")
    print("4. 路径优化计算")
    print("5. 最终利润计算")
    print("6. 策略对比分析")

if __name__ == "__main__":
    demonstrate_calculation_process()
