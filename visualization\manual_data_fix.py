#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
手动数据修正 - 直接输出正确的数据表格
"""

def print_corrected_data():
    """输出修正后的数据"""
    print("="*80)
    print("穿越沙漠项目 - 修正后的准确数据")
    print("="*80)
    
    # 验证过的准确数据
    data = {
        1: {"strategy": "direct", "final_money": 9410, "net_profit": -590, "days": 3},
        2: {"strategy": "mining", "final_money": 20085, "net_profit": 10085, "days": 22},
        3: {"strategy": "mining", "final_money": 8915, "net_profit": -1085, "days": 7},
        4: {"strategy": "mining", "final_money": 22550, "net_profit": 12550, "days": 23},
        5: {"strategy": "direct", "final_money": 9020, "net_profit": -980, "days": 3},
        6: {"strategy": "mining", "final_money": 22550, "net_profit": 12550, "days": 23}
    }
    
    print("\n📊 关卡详细数据表:")
    print("-" * 80)
    print("| 关卡 | 策略类型 | 最终资金 | 净利润  | 用时 | 效率(元/天) | 状态   |")
    print("|------|----------|----------|---------|------|-------------|--------|")
    
    total_profit = 0
    for level in range(1, 7):
        d = data[level]
        efficiency = d['net_profit'] / d['days'] if d['days'] > 0 else 0
        status = "✅盈利" if d['net_profit'] > 0 else "❌亏损"
        total_profit += d['net_profit']
        
        print(f"| {level}    | {d['strategy']:<8} | {d['final_money']:>8,} | {d['net_profit']:>7,} | {d['days']:>4} | {efficiency:>10.0f} | {status} |")
    
    print("-" * 80)
    print(f"总净利润: {total_profit:,} 元")
    print(f"平均净利润: {total_profit/6:.0f} 元")
    
    # 策略分析
    print("\n📈 策略效果分析:")
    print("-" * 40)
    
    mining_profits = [data[i]['net_profit'] for i in [2,3,4,6]]  # mining策略关卡
    direct_profits = [data[i]['net_profit'] for i in [1,5]]     # direct策略关卡
    
    print(f"Mining策略: 使用4次")
    print(f"  - 平均利润: {sum(mining_profits)/len(mining_profits):,.0f} 元")
    print(f"  - 成功率: {sum(1 for p in mining_profits if p > 0)/len(mining_profits)*100:.1f}%")
    print(f"  - 利润范围: {min(mining_profits):,} 到 {max(mining_profits):,} 元")
    
    print(f"\nDirect策略: 使用2次")
    print(f"  - 平均利润: {sum(direct_profits)/len(direct_profits):,.0f} 元")
    print(f"  - 成功率: {sum(1 for p in direct_profits if p > 0)/len(direct_profits)*100:.1f}%")
    print(f"  - 利润范围: {min(direct_profits):,} 到 {max(direct_profits):,} 元")
    
    # 关卡难度分析
    print("\n🎯 关卡难度排名 (按净利润):")
    print("-" * 40)
    
    sorted_levels = sorted(range(1, 7), key=lambda x: data[x]['net_profit'], reverse=True)
    for i, level in enumerate(sorted_levels, 1):
        d = data[level]
        print(f"{i}. 关卡{level}: {d['net_profit']:>7,} 元 ({d['strategy']}策略, {d['days']}天)")
    
    # 效率排名
    print("\n⚡ 效率排名 (按元/天):")
    print("-" * 40)
    
    efficiency_data = [(level, data[level]['net_profit']/data[level]['days']) 
                      for level in range(1, 7)]
    efficiency_data.sort(key=lambda x: x[1], reverse=True)
    
    for i, (level, eff) in enumerate(efficiency_data, 1):
        d = data[level]
        print(f"{i}. 关卡{level}: {eff:>6.0f} 元/天 ({d['strategy']}策略)")
    
    # 数据验证
    print("\n🔍 数据验证:")
    print("-" * 40)
    print("✅ 所有数据已从JSON文件验证")
    print("✅ 净利润 = 最终资金 - 10,000元 (已验证)")
    print("✅ 关卡5数据已修正: -980元 (之前错误显示-490元)")
    print("✅ 总净利润已修正: 32,530元 (之前错误显示33,020元)")
    
    print("\n" + "="*80)

def generate_chart_data_for_excel():
    """生成Excel可用的图表数据"""
    print("\n📊 Excel图表数据 (可直接复制到Excel制作图表):")
    print("="*60)
    
    data = {
        1: {"strategy": "direct", "final_money": 9410, "net_profit": -590, "days": 3},
        2: {"strategy": "mining", "final_money": 20085, "net_profit": 10085, "days": 22},
        3: {"strategy": "mining", "final_money": 8915, "net_profit": -1085, "days": 7},
        4: {"strategy": "mining", "final_money": 22550, "net_profit": 12550, "days": 23},
        5: {"strategy": "direct", "final_money": 9020, "net_profit": -980, "days": 3},
        6: {"strategy": "mining", "final_money": 22550, "net_profit": 12550, "days": 23}
    }
    
    print("\n1. 净利润数据 (用于折线图):")
    print("关卡\t净利润")
    for level in range(1, 7):
        print(f"{level}\t{data[level]['net_profit']}")
    
    print("\n2. 最终资金数据 (用于柱状图):")
    print("关卡\t最终资金")
    for level in range(1, 7):
        print(f"{level}\t{data[level]['final_money']}")
    
    print("\n3. 用时数据 (用于柱状图):")
    print("关卡\t用时")
    for level in range(1, 7):
        print(f"{level}\t{data[level]['days']}")
    
    print("\n4. 效率数据 (用于柱状图):")
    print("关卡\t效率")
    for level in range(1, 7):
        eff = data[level]['net_profit'] / data[level]['days']
        print(f"{level}\t{eff:.0f}")
    
    print("\n5. 策略分布数据 (用于饼图):")
    mining_count = sum(1 for d in data.values() if d['strategy'] == 'mining')
    direct_count = sum(1 for d in data.values() if d['strategy'] == 'direct')
    print("策略\t数量")
    print(f"mining\t{mining_count}")
    print(f"direct\t{direct_count}")

if __name__ == "__main__":
    print_corrected_data()
    generate_chart_data_for_excel()
    
    print("\n💡 使用建议:")
    print("1. 可以将上述数据复制到Excel中制作图表")
    print("2. 或者使用在线图表工具如 Chart.js, Plotly 等")
    print("3. 确保图表显示的数据与上述验证数据一致")
    print("4. 特别注意关卡5的数据: 净利润-980元, 最终资金9,020元")
