#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
修正版求解器
严格按照原题规则实现
"""

from typing import Dict, List, Optional, Tuple, Set
import random
from dataclasses import dataclass
from collections import deque

try:
    from config.game_config import LevelConfig
    from utils.path_finder import PathFinder
    from utils.consumption_calculator import ConsumptionCalculator
except ImportError as e:
    print(f"导入模块失败: {e}")
    raise

@dataclass
class GameState:
    """游戏状态"""
    day: int
    location: int
    water: int
    food: int
    money: int
    path_history: List[int]
    action_history: List[str]

@dataclass
class CorrectedSolutionResult:
    """修正版求解结果"""
    level: int
    is_feasible: bool
    final_money: int
    final_day: int
    path: List[int]
    daily_log: List[Dict]
    strategy_type: str  # "direct", "mining", "village"
    
class CorrectedLevelSolver:
    """修正版关卡求解器"""
    
    def __init__(self, config: LevelConfig):
        self.config = config
        self.path_finder = PathFinder(config)
        self.consumption_calc = ConsumptionCalculator(config)
        # 为了向后兼容，添加mine_node属性
        if hasattr(config, 'mine_nodes') and config.mine_nodes:
            self.config.mine_node = config.mine_nodes[0]
        elif not hasattr(config, 'mine_node'):
            self.config.mine_node = None
        
    def solve(self) -> Optional[CorrectedSolutionResult]:
        """求解关卡 - 严格按照原题规则"""
        print(f"修正版求解器 - 第{self.config.level}关")
        
        if self.config.level in [1, 2, 5]:
            # 天气完全已知的关卡
            return self._solve_with_known_weather()
        elif self.config.level in [3, 4, 6]:
            # 仅知当天天气的关卡
            return self._solve_with_daily_weather()
        else:
            print(f"未知关卡类型: {self.config.level}")
            return None
    
    def _solve_with_known_weather(self) -> Optional[CorrectedSolutionResult]:
        """求解天气完全已知的关卡"""
        print("天气完全已知，使用动态规划求解")
        
        best_result = None
        strategies = self._generate_all_strategies()
        
        for strategy in strategies:
            result = self._evaluate_strategy_with_known_weather(strategy)
            if result and result.is_feasible:
                if best_result is None or result.final_money > best_result.final_money:
                    best_result = result
        
        return best_result
    
    def _solve_with_daily_weather(self) -> Optional[CorrectedSolutionResult]:
        """求解仅知当天天气的关卡"""
        print("仅知当天天气，使用贪心策略")
        
        # 对于仅知当天天气的情况，使用保守的贪心策略
        return self._greedy_solve()
    
    def _generate_all_strategies(self) -> List[Dict]:
        """生成所有可能的策略"""
        strategies = []
        
        # 策略1: 直接到终点（不经过矿山）
        direct_path = self._find_direct_path()
        if direct_path:
            strategies.append({
                'type': 'direct',
                'path': direct_path,
                'mining_days': 0
            })
        
        # 策略2: 经过矿山的各种挖矿策略
        if self.config.mine_nodes and self.config.mine_nodes[0] in self.config.map_nodes:
            mining_paths = self._find_mining_paths()
            for path in mining_paths:
                for mining_days in range(0, 16):  # 尝试不同的挖矿天数
                    strategies.append({
                        'type': 'mining',
                        'path': path,
                        'mining_days': mining_days
                    })
        
        # 策略3: 经过村庄的策略
        if self.config.village_nodes:
            village_paths = self._find_village_paths()
            for path in village_paths:
                strategies.append({
                    'type': 'village',
                    'path': path,
                    'mining_days': 0
                })
        
        return strategies
    
    def _find_direct_path(self) -> Optional[List[int]]:
        """找到直接到终点的最短路径"""
        return self.path_finder._find_shortest_path(
            self.config.start_node, 
            self.config.end_node
        )
    
    def _find_mining_paths(self) -> List[List[int]]:
        """找到经过矿山的路径"""
        paths = []
        
        # 起点 -> 矿山 -> 终点
        mine_node = self.config.mine_nodes[0] if self.config.mine_nodes else None
        if mine_node is None:
            return paths

        path_to_mine = self.path_finder._find_shortest_path(
            self.config.start_node,
            mine_node
        )
        path_from_mine = self.path_finder._find_shortest_path(
            mine_node,
            self.config.end_node
        )
        
        if path_to_mine and path_from_mine:
            full_path = path_to_mine + path_from_mine[1:]  # 避免重复矿山节点
            paths.append(full_path)
        
        return paths
    
    def _find_village_paths(self) -> List[List[int]]:
        """找到经过村庄的路径"""
        paths = []
        
        for village in self.config.village_nodes:
            # 起点 -> 村庄 -> 终点
            path_to_village = self.path_finder._find_shortest_path(
                self.config.start_node, village
            )
            path_from_village = self.path_finder._find_shortest_path(
                village, self.config.end_node
            )
            
            if path_to_village and path_from_village:
                full_path = path_to_village + path_from_village[1:]
                paths.append(full_path)
        
        return paths
    
    def _evaluate_strategy_with_known_weather(self, strategy: Dict) -> Optional[CorrectedSolutionResult]:
        """评估策略（天气已知情况）"""
        path = strategy['path']
        mining_days = strategy.get('mining_days', 0)
        
        # 模拟执行策略
        state = GameState(
            day=0,
            location=self.config.start_node,
            water=0,
            food=0,
            money=self.config.initial_money,
            path_history=[self.config.start_node],
            action_history=['start']
        )
        
        daily_log = []
        
        # 第0天：在起点购买资源
        initial_resources = self._calculate_required_resources(strategy)
        if not initial_resources:
            return None
            
        water_needed, food_needed = initial_resources
        purchase_cost = water_needed * self.config.water_price + food_needed * self.config.food_price
        
        if purchase_cost > state.money:
            return None  # 资金不足
        
        # 检查负重约束
        total_weight = water_needed * self.config.water_weight + food_needed * self.config.food_weight
        if total_weight > self.config.max_weight:
            return None  # 超重
        
        state.water = water_needed
        state.food = food_needed
        state.money -= purchase_cost
        
        # 执行路径
        path_index = 0
        mine_reached = False
        mining_days_left = mining_days
        
        while state.day < self.config.deadline_days and path_index < len(path) - 1:
            state.day += 1
            
            # 获取当天天气
            weather = self._get_weather(state.day)
            
            # 检查是否是沙暴日
            if weather == '沙暴':
                # 沙暴日必须停留
                action = 'stay_storm'
                consumption = self.consumption_calc.get_daily_consumption(weather, 'stay')
                income = 0
                
                # 如果在矿山且还有挖矿天数，可以挖矿
                mine_node = self.config.mine_nodes[0] if self.config.mine_nodes else None
                if (mine_node and state.location == mine_node and
                    mining_days_left > 0 and mine_reached):
                    action = 'mine_storm'
                    income = self.consumption_calc.get_mining_income(weather)
                    mining_days_left -= 1
                
            else:
                # 非沙暴日可以选择行动
                mine_node = self.config.mine_nodes[0] if self.config.mine_nodes else None
                if (mine_node and state.location == mine_node and
                    mining_days_left > 0 and mine_reached):
                    # 在矿山挖矿
                    action = 'mine'
                    consumption = self.consumption_calc.get_daily_consumption(weather, 'stay')
                    income = self.consumption_calc.get_mining_income(weather)
                    mining_days_left -= 1
                else:
                    # 移动到下一个位置
                    if path_index < len(path) - 1:
                        next_location = path[path_index + 1]
                        action = f'move_to_{next_location}'
                        consumption = self.consumption_calc.get_daily_consumption(weather, 'move')
                        income = 0
                        
                        state.location = next_location
                        state.path_history.append(next_location)
                        path_index += 1
                        
                        mine_node = self.config.mine_nodes[0] if self.config.mine_nodes else None
                        if mine_node and next_location == mine_node:
                            mine_reached = True
                    else:
                        # 已到达终点
                        action = 'stay_end'
                        consumption = self.consumption_calc.get_daily_consumption(weather, 'stay')
                        income = 0
            
            # 消耗资源
            state.water -= consumption['water']
            state.food -= consumption['food']
            state.money += income
            
            # 检查资源是否足够
            if state.water < 0 or state.food < 0:
                return None  # 资源不足，策略失败
            
            daily_log.append({
                'day': state.day,
                'location': state.location,
                'weather': weather,
                'action': action,
                'water_consumed': consumption['water'],
                'food_consumed': consumption['food'],
                'income': income,
                'remaining_water': state.water,
                'remaining_food': state.food,
                'remaining_money': state.money
            })
            
            state.action_history.append(action)
        
        # 检查是否成功到达终点
        if state.location != self.config.end_node:
            return None
        
        # 退回剩余资源（按基准价格的一半）
        refund = (state.water * self.config.water_price + 
                 state.food * self.config.food_price) // 2
        state.money += refund
        
        return CorrectedSolutionResult(
            level=self.config.level,
            is_feasible=True,
            final_money=state.money,
            final_day=state.day,
            path=state.path_history,
            daily_log=daily_log,
            strategy_type=strategy['type']
        )
    
    def _calculate_required_resources(self, strategy: Dict) -> Optional[Tuple[int, int]]:
        """计算策略所需的资源量"""
        path = strategy['path']
        mining_days = strategy.get('mining_days', 0)

        total_days = len(path) - 1 + mining_days
        if total_days > self.config.deadline_days:
            return None

        # 根据天气预报精确计算（如果已知）或保守估计
        if self.config.weather_known and self.config.weather_forecast:
            return self._calculate_precise_resources(strategy)
        else:
            return self._calculate_conservative_resources(strategy)

    def _calculate_precise_resources(self, strategy: Dict) -> Tuple[int, int]:
        """根据已知天气精确计算资源需求"""
        path = strategy['path']
        mining_days = strategy.get('mining_days', 0)

        total_water = 0
        total_food = 0
        current_day = 0

        # 计算移动消耗
        mine_node = self.config.mine_nodes[0] if self.config.mine_nodes else None
        mine_index = path.index(mine_node) if mine_node and mine_node in path else len(path)

        for i in range(len(path) - 1):
            current_day += 1
            if current_day <= len(self.config.weather_forecast):
                weather = self.config.weather_forecast[current_day - 1]
                consumption = self.consumption_calc.get_daily_consumption(weather, 'move')
                total_water += consumption['water']
                total_food += consumption['food']

        # 计算挖矿消耗
        for day in range(mining_days):
            current_day += 1
            if current_day <= len(self.config.weather_forecast):
                weather = self.config.weather_forecast[current_day - 1]
                consumption = self.consumption_calc.get_daily_consumption(weather, 'stay')
                total_water += consumption['water']
                total_food += consumption['food']

        return (total_water, total_food)

    def _calculate_conservative_resources(self, strategy: Dict) -> Tuple[int, int]:
        """保守估计资源需求"""
        path = strategy['path']
        mining_days = strategy.get('mining_days', 0)

        # 使用更合理的天气估计（高温天气而不是沙暴）
        # 因为沙暴日不能移动，实际移动多在晴朗/高温天气
        high_temp_consumption = self.config.consumption.get('高温', {'water': 9, 'food': 9})
        base_consumption = self.config.consumption.get('晴朗', {'water': 3, 'food': 4})

        # 移动消耗（使用高温天气估计）
        move_days = len(path) - 1
        move_water = move_days * high_temp_consumption['water'] * self.config.move_factor
        move_food = move_days * high_temp_consumption['food'] * self.config.move_factor

        # 挖矿消耗（使用基础消耗，因为挖矿时停留）
        mine_water = mining_days * base_consumption['water']
        mine_food = mining_days * base_consumption['food']

        # 适度安全边际
        safety_factor = 1.1  # 降低安全系数
        total_water = int((move_water + mine_water) * safety_factor)
        total_food = int((move_food + mine_food) * safety_factor)

        return (total_water, total_food)
    
    def _get_weather(self, day: int) -> str:
        """获取指定天数的天气"""
        if day <= len(self.config.weather_forecast):
            return self.config.weather_forecast[day - 1]
        else:
            return '晴朗'  # 默认天气
    
    def _greedy_solve(self) -> Optional[CorrectedSolutionResult]:
        """贪心求解（仅知当天天气）"""
        print("使用保守策略求解仅知当天天气的关卡")

        # 对于仅知当天天气的情况，尝试多种保守策略
        strategies = []

        # 策略1: 直接到终点
        direct_path = self._find_direct_path()
        if direct_path:
            strategies.append({
                'type': 'direct',
                'path': direct_path,
                'mining_days': 0
            })

        # 策略2: 经过矿山但不挖矿（快速通过）
        mining_paths = self._find_mining_paths()
        for path in mining_paths:
            strategies.append({
                'type': 'mining',
                'path': path,
                'mining_days': 0
            })

        # 策略3: 经过矿山并优化挖矿天数
        for path in mining_paths:
            # 根据关卡特点调整挖矿天数范围
            if self.config.level == 3:
                # 第3关时间短，基础收益低，少量挖矿
                mining_days_options = [1, 2]
            elif self.config.level in [4, 6]:
                # 第4、6关基础收益高，增加挖矿天数
                mining_days_options = [1, 2, 3, 5, 8, 10, 12, 15]
            else:
                mining_days_options = [1, 2, 3, 5]

            for mining_days in mining_days_options:
                # 确保不超过时间限制
                path_days = len(path) - 1
                if path_days + mining_days <= self.config.deadline_days - 2:  # 留2天缓冲
                    strategies.append({
                        'type': 'mining',
                        'path': path,
                        'mining_days': mining_days
                    })

        # 评估所有策略，选择最保守可行的
        best_result = None

        for strategy in strategies:
            # 使用保守的资源估计
            result = self._evaluate_conservative_strategy(strategy)
            if result and result.is_feasible:
                if best_result is None or result.final_money > best_result.final_money:
                    best_result = result

        return best_result

    def _evaluate_conservative_strategy(self, strategy: Dict) -> Optional[CorrectedSolutionResult]:
        """评估保守策略（适用于天气未知情况）"""
        path = strategy['path']
        mining_days = strategy.get('mining_days', 0)

        # 使用保守的资源估计
        resources = self._calculate_conservative_resources(strategy)
        if not resources:
            return None

        water_needed, food_needed = resources

        # 检查约束
        purchase_cost = water_needed * self.config.water_price + food_needed * self.config.food_price
        if purchase_cost > self.config.initial_money:
            return None

        total_weight = water_needed * self.config.water_weight + food_needed * self.config.food_weight
        if total_weight > self.config.max_weight:
            return None

        total_days = len(path) - 1 + mining_days
        if total_days > self.config.deadline_days:
            return None

        # 估算收入（更准确的估计）
        # 挖矿收入 = 基础收益 + 基础水消耗*5 + 基础食物消耗*10（根据题目规则）
        base_consumption = self.config.consumption.get('晴朗', {'water': 3, 'food': 4})
        daily_mining_income = (self.config.base_income +
                              base_consumption['water'] * self.config.water_price +
                              base_consumption['food'] * self.config.food_price)
        total_income = mining_days * daily_mining_income

        # 计算最终资金
        final_money = self.config.initial_money - purchase_cost + total_income

        # 生成详细的每日日志
        daily_log = []
        current_location = path[0]  # 起始位置
        path_index = 0

        for day in range(1, total_days + 1):
            # 获取当天天气
            if hasattr(self.config, 'weather_forecast') and self.config.weather_forecast and day <= len(self.config.weather_forecast):
                weather = self.config.weather_forecast[day - 1]
            else:
                # 超出预报范围，使用默认天气
                weather = '晴朗'

            # 确定当天的行动和位置
            if day <= len(path) - 1:
                # 移动阶段
                action = 'move'
                if day > 1:  # 第一天之后才移动
                    path_index = min(day - 1, len(path) - 1)
                    current_location = path[path_index]
                consumption = self.consumption_calc.get_daily_consumption(weather, 'move')
                income = 0
            else:
                # 挖矿阶段
                action = 'mine'
                current_location = path[-1]  # 在最后位置（矿山）
                consumption = self.consumption_calc.get_daily_consumption(weather, 'stay')
                income = daily_mining_income

            daily_log.append({
                'day': day,
                'location': current_location,
                'weather': weather,
                'action': action,
                'water_consumed': consumption['water'],
                'food_consumed': consumption['food'],
                'income': income
            })

        return CorrectedSolutionResult(
            level=self.config.level,
            is_feasible=True,
            final_money=final_money,
            final_day=total_days,
            path=path,
            daily_log=daily_log,
            strategy_type=strategy['type']
        )
